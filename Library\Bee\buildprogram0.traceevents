{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1754341037567778, "dur": 331952, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037568912, "dur": 55552, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037633243, "dur": 227954, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037634017, "dur": 180274, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037634547, "dur": 42530, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037677491, "dur": 708, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037680015, "dur": 132424, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037868243, "dur": 1154, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037869399, "dur": 30323, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037870896, "dur": 24226, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037907666, "dur": 1453, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1754341037907236, "dur": 2154, "ph": "X", "name": "Write chrome-trace events", "args": {} },
