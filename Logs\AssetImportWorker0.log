Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-01T19:59:18Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker0.log
-srvPort
59354
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [119840]  Target information:

Player connection [119840]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1945873116 [EditorId] 1945873116 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [119840]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1945873116 [EditorId] 1945873116 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [119840]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1945873116 [EditorId] 1945873116 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [119840]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1945873116 [EditorId] 1945873116 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [119840]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1945873116 [EditorId] 1945873116 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [119840] Host joined multi-casting on [***********:54997]...
Player connection [119840] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2196.99 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56104
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.012306 seconds.
- Loaded All Assemblies, in 16.518 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.870 seconds
Domain Reload Profiling: 17372ms
	BeginReloadAssembly (13021ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (1244ms)
	RebuildNativeTypeToScriptingClass (46ms)
	initialDomainReloadingComplete (284ms)
	LoadAllAssembliesAndSetupDomain (1908ms)
		LoadAssemblies (13002ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1899ms)
			TypeCache.Refresh (1894ms)
				TypeCache.ScanAssembly (1392ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (870ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (783ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (77ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (93ms)
			ProcessInitializeOnLoadAttributes (416ms)
			ProcessInitializeOnLoadMethodAttributes (191ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.14 seconds
Error loading the file 'Library/BuildProfiles/SharedProfile.asset'. File is either empty or corrupted, please verify the file contents.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForgetInternal (string,UnityEngine.Object[],bool)
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForget (UnityEngine.Object[],string,bool)
UnityEditor.Build.Profile.BuildProfileContext:SaveBuildProfileInProject (UnityEditor.Build.Profile.BuildProfile)
UnityEditor.Build.Profile.BuildProfileContext:OnDisable ()

[ line 0]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 67.714 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 116.78 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.619 seconds
Domain Reload Profiling: 76293ms
	BeginReloadAssembly (44875ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (241ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (148ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (151ms)
	LoadAllAssembliesAndSetupDomain (22463ms)
		LoadAssemblies (21837ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1585ms)
			TypeCache.Refresh (1389ms)
				TypeCache.ScanAssembly (1280ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (8620ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (7944ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (999ms)
			ProcessInitializeOnLoadAttributes (2087ms)
			ProcessInitializeOnLoadMethodAttributes (4780ms)
			AfterProcessingInitializeOnLoad (68ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (67ms)
Refreshing native plugins compatible for Editor in 268.98 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 553 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7919 unused Assets / (8.6 MB). Loaded Objects now: 8986.
Memory consumption went from 382.0 MB to 373.5 MB.
Total: 244.225200 ms (FindLiveObjects: 9.426900 ms CreateObjectMapping: 1.637500 ms MarkObjects: 111.313600 ms  DeleteObjects: 121.845500 ms)

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 135.87 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (6.8 MB). Loaded Objects now: 8979.
Memory consumption went from 303.6 MB to 296.7 MB.
Total: 425.728100 ms (FindLiveObjects: 22.964900 ms CreateObjectMapping: 29.763800 ms MarkObjects: 331.620600 ms  DeleteObjects: 41.377400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 358003.306312 seconds.
  path: Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/ClimbingRopeSystem.cs
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/ClimbingRopeSystem.cs using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e7f6514916746a1194b6795c88c77e6') in 0.055802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 47.12 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (5.3 MB). Loaded Objects now: 8981.
Memory consumption went from 304.1 MB to 298.8 MB.
Total: 84.677000 ms (FindLiveObjects: 1.828600 ms CreateObjectMapping: 1.125400 ms MarkObjects: 55.126600 ms  DeleteObjects: 26.594900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.83 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 8981.
Memory consumption went from 304.1 MB to 301.2 MB.
Total: 2789.331800 ms (FindLiveObjects: 289.596300 ms CreateObjectMapping: 1894.605200 ms MarkObjects: 456.112900 ms  DeleteObjects: 149.015300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1131.13 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 8981.
Memory consumption went from 304.1 MB to 301.2 MB.
Total: 155.214600 ms (FindLiveObjects: 2.640700 ms CreateObjectMapping: 3.547900 ms MarkObjects: 140.884100 ms  DeleteObjects: 8.139900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.99 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 8981.
Memory consumption went from 304.1 MB to 301.2 MB.
Total: 2448.512200 ms (FindLiveObjects: 59.386100 ms CreateObjectMapping: 2033.303500 ms MarkObjects: 315.388000 ms  DeleteObjects: 40.432000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.210 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.98 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.891 seconds
Domain Reload Profiling: 9997ms
	BeginReloadAssembly (1154ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (139ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (185ms)
	RebuildCommonClasses (146ms)
	RebuildNativeTypeToScriptingClass (37ms)
	initialDomainReloadingComplete (206ms)
	LoadAllAssembliesAndSetupDomain (3561ms)
		LoadAssemblies (2888ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1244ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (31ms)
			BuildScriptInfoCaches (1150ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (4893ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3649ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (53ms)
			BeforeProcessingInitializeOnLoad (1269ms)
			ProcessInitializeOnLoadAttributes (1859ms)
			ProcessInitializeOnLoadMethodAttributes (416ms)
			AfterProcessingInitializeOnLoad (42ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (80ms)
Refreshing native plugins compatible for Editor in 5866.01 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9004.
Memory consumption went from 320.8 MB to 318.0 MB.
Total: 1010.890500 ms (FindLiveObjects: 74.799600 ms CreateObjectMapping: 373.175000 ms MarkObjects: 140.040400 ms  DeleteObjects: 422.874100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1208.013623 seconds.
  path: Assets/_Game/Resources/ItemsIcons/Rope.png
  artifactKey: Guid(1df668a5a178b484cb369bfe829760c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/ItemsIcons/Rope.png using Guid(1df668a5a178b484cb369bfe829760c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '95385fe98dc41a89b6bb47fa307b60a4') in 1.1618821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 6.352419 seconds.
  path: Assets/_Game/Resources/Items/ClimbingRope.asset
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/ClimbingRope.asset using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '75cec87ae2aaec747fa0e079ad3ea132') in 0.012274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1890.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9010.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 698.991200 ms (FindLiveObjects: 534.174500 ms CreateObjectMapping: 1.568400 ms MarkObjects: 116.198100 ms  DeleteObjects: 47.048100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.854 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 62.39 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.543 seconds
Domain Reload Profiling: 15217ms
	BeginReloadAssembly (1372ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (167ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (195ms)
	RebuildCommonClasses (222ms)
	RebuildNativeTypeToScriptingClass (81ms)
	initialDomainReloadingComplete (545ms)
	LoadAllAssembliesAndSetupDomain (7454ms)
		LoadAssemblies (6485ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1735ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (1563ms)
			ResolveRequiredComponents (87ms)
	FinalizeReload (5544ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4075ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (831ms)
			ProcessInitializeOnLoadAttributes (2295ms)
			ProcessInitializeOnLoadMethodAttributes (738ms)
			AfterProcessingInitializeOnLoad (182ms)
			EditorAssembliesLoaded (6ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (181ms)
Refreshing native plugins compatible for Editor in 32.07 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9011.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 500.688700 ms (FindLiveObjects: 327.211600 ms CreateObjectMapping: 7.356800 ms MarkObjects: 144.399100 ms  DeleteObjects: 21.719700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 156.40 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7857 unused Assets / (2.9 MB). Loaded Objects now: 9010.
Memory consumption went from 317.5 MB to 314.6 MB.
Total: 226.624500 ms (FindLiveObjects: 2.885100 ms CreateObjectMapping: 2.373100 ms MarkObjects: 212.654200 ms  DeleteObjects: 8.710500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.769 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 157.66 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.143 seconds
Domain Reload Profiling: 7871ms
	BeginReloadAssembly (899ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (167ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (140ms)
	RebuildCommonClasses (99ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (2579ms)
		LoadAssemblies (2095ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (860ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (790ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (4143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (36ms)
			BeforeProcessingInitializeOnLoad (1138ms)
			ProcessInitializeOnLoadAttributes (1038ms)
			ProcessInitializeOnLoadMethodAttributes (228ms)
			AfterProcessingInitializeOnLoad (31ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (87ms)
Refreshing native plugins compatible for Editor in 352.70 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.7 MB). Loaded Objects now: 9012.
Memory consumption went from 317.8 MB to 314.1 MB.
Total: 121.819200 ms (FindLiveObjects: 7.874900 ms CreateObjectMapping: 1.388000 ms MarkObjects: 76.206900 ms  DeleteObjects: 36.347900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.084 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 83.01 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.012 seconds
Domain Reload Profiling: 12987ms
	BeginReloadAssembly (805ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (167ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (126ms)
	LoadAllAssembliesAndSetupDomain (6949ms)
		LoadAssemblies (4213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (3075ms)
			TypeCache.Refresh (43ms)
				TypeCache.ScanAssembly (11ms)
			BuildScriptInfoCaches (2791ms)
			ResolveRequiredComponents (159ms)
	FinalizeReload (5013ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3685ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (33ms)
			BeforeProcessingInitializeOnLoad (1021ms)
			ProcessInitializeOnLoadAttributes (1733ms)
			ProcessInitializeOnLoadMethodAttributes (804ms)
			AfterProcessingInitializeOnLoad (92ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (261ms)
Refreshing native plugins compatible for Editor in 211.38 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (4.0 MB). Loaded Objects now: 9014.
Memory consumption went from 317.8 MB to 313.8 MB.
Total: 82.000400 ms (FindLiveObjects: 5.836100 ms CreateObjectMapping: 20.288000 ms MarkObjects: 46.636500 ms  DeleteObjects: 9.238400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2512.608557 seconds.
  path: Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook
  artifactKey: Guid(23d6bf373e607344785f6a36c8714d6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook using Guid(23d6bf373e607344785f6a36c8714d6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3f863573240716f52b853be661fa44b4') in 0.2607657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 12.780900 seconds.
  path: Assets/_Game/Resources/Items/Rope.asset
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/Rope.asset using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '816d48875c723ba4b78074675b9ee066') in 0.1231728 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.179 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 43.59 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.902 seconds
Domain Reload Profiling: 10922ms
	BeginReloadAssembly (1630ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (153ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (275ms)
	RebuildCommonClasses (113ms)
	RebuildNativeTypeToScriptingClass (88ms)
	initialDomainReloadingComplete (386ms)
	LoadAllAssembliesAndSetupDomain (4801ms)
		LoadAssemblies (4062ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1608ms)
			TypeCache.Refresh (55ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (1485ms)
			ResolveRequiredComponents (60ms)
	FinalizeReload (3903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2992ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (736ms)
			ProcessInitializeOnLoadAttributes (1834ms)
			ProcessInitializeOnLoadMethodAttributes (360ms)
			AfterProcessingInitializeOnLoad (45ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (111ms)
Refreshing native plugins compatible for Editor in 291.37 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.4 MB). Loaded Objects now: 9016.
Memory consumption went from 317.8 MB to 314.4 MB.
Total: 91.644100 ms (FindLiveObjects: 4.815900 ms CreateObjectMapping: 11.063200 ms MarkObjects: 64.612800 ms  DeleteObjects: 11.150300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.45 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7857 unused Assets / (3.8 MB). Loaded Objects now: 9016.
Memory consumption went from 317.5 MB to 313.7 MB.
Total: 1346.966400 ms (FindLiveObjects: 1.528000 ms CreateObjectMapping: 5.292100 ms MarkObjects: 1289.925600 ms  DeleteObjects: 50.218400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.353 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 77.58 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.207 seconds
Domain Reload Profiling: 9489ms
	BeginReloadAssembly (1482ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (191ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (148ms)
	RebuildCommonClasses (221ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (288ms)
	LoadAllAssembliesAndSetupDomain (3269ms)
		LoadAssemblies (2783ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1338ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (1073ms)
			ResolveRequiredComponents (110ms)
	FinalizeReload (4207ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3111ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (1062ms)
			ProcessInitializeOnLoadAttributes (1614ms)
			ProcessInitializeOnLoadMethodAttributes (348ms)
			AfterProcessingInitializeOnLoad (67ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (114ms)
Refreshing native plugins compatible for Editor in 3233.93 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (4.4 MB). Loaded Objects now: 9018.
Memory consumption went from 317.8 MB to 313.4 MB.
Total: 114.704900 ms (FindLiveObjects: 13.214500 ms CreateObjectMapping: 13.515500 ms MarkObjects: 69.751800 ms  DeleteObjects: 18.220100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.188 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 74.81 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.274 seconds
Domain Reload Profiling: 16304ms
	BeginReloadAssembly (2239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (195ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (428ms)
	RebuildCommonClasses (268ms)
	RebuildNativeTypeToScriptingClass (83ms)
	initialDomainReloadingComplete (387ms)
	LoadAllAssembliesAndSetupDomain (7051ms)
		LoadAssemblies (6511ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1630ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (1430ms)
			ResolveRequiredComponents (118ms)
	FinalizeReload (6275ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4843ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (1147ms)
			ProcessInitializeOnLoadAttributes (2893ms)
			ProcessInitializeOnLoadMethodAttributes (681ms)
			AfterProcessingInitializeOnLoad (95ms)
			EditorAssembliesLoaded (6ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (398ms)
Refreshing native plugins compatible for Editor in 2687.32 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9020.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 1533.937300 ms (FindLiveObjects: 3.199400 ms CreateObjectMapping: 1.535300 ms MarkObjects: 1499.241200 ms  DeleteObjects: 29.959600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.904 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 139.77 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.516 seconds
Domain Reload Profiling: 13438ms
	BeginReloadAssembly (1522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (185ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (399ms)
	RebuildCommonClasses (156ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (237ms)
	LoadAllAssembliesAndSetupDomain (4975ms)
		LoadAssemblies (3521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1887ms)
			TypeCache.Refresh (65ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (1708ms)
			ResolveRequiredComponents (51ms)
	FinalizeReload (6516ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4442ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (27ms)
			BeforeProcessingInitializeOnLoad (1468ms)
			ProcessInitializeOnLoadAttributes (2324ms)
			ProcessInitializeOnLoadMethodAttributes (539ms)
			AfterProcessingInitializeOnLoad (75ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (142ms)
Refreshing native plugins compatible for Editor in 42.28 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.0 MB). Loaded Objects now: 9022.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 33.680500 ms (FindLiveObjects: 1.329200 ms CreateObjectMapping: 1.646200 ms MarkObjects: 25.041100 ms  DeleteObjects: 5.662500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 15.488 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 328.79 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  9.408 seconds
Domain Reload Profiling: 23667ms
	BeginReloadAssembly (2430ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (292ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (375ms)
	RebuildCommonClasses (1434ms)
	RebuildNativeTypeToScriptingClass (395ms)
	initialDomainReloadingComplete (676ms)
	LoadAllAssembliesAndSetupDomain (9322ms)
		LoadAssemblies (8030ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2632ms)
			TypeCache.Refresh (89ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (2475ms)
			ResolveRequiredComponents (54ms)
	FinalizeReload (9409ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (30ms)
			BeforeProcessingInitializeOnLoad (2189ms)
			ProcessInitializeOnLoadAttributes (2644ms)
			ProcessInitializeOnLoadMethodAttributes (362ms)
			AfterProcessingInitializeOnLoad (61ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (200ms)
Refreshing native plugins compatible for Editor in 2992.30 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9024.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 1210.445700 ms (FindLiveObjects: 2.674400 ms CreateObjectMapping: 1.476700 ms MarkObjects: 1199.615500 ms  DeleteObjects: 6.626500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 15.900 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 35.78 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.956 seconds
Domain Reload Profiling: 22687ms
	BeginReloadAssembly (5125ms)
		ExecutionOrderSort (5ms)
		DisableScriptedObjects (463ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (7ms)
		CreateAndSetChildDomain (498ms)
	RebuildCommonClasses (574ms)
	RebuildNativeTypeToScriptingClass (144ms)
	initialDomainReloadingComplete (226ms)
	LoadAllAssembliesAndSetupDomain (9660ms)
		LoadAssemblies (12176ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1020ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (948ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (6958ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5356ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (29ms)
			BeforeProcessingInitializeOnLoad (1749ms)
			ProcessInitializeOnLoadAttributes (2152ms)
			ProcessInitializeOnLoadMethodAttributes (1228ms)
			AfterProcessingInitializeOnLoad (188ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (338ms)
Refreshing native plugins compatible for Editor in 2612.50 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9026.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 157.546500 ms (FindLiveObjects: 2.393000 ms CreateObjectMapping: 12.081800 ms MarkObjects: 119.299600 ms  DeleteObjects: 23.770200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.822 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 157.26 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 26.082 seconds
Domain Reload Profiling: 36441ms
	BeginReloadAssembly (2160ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (581ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (472ms)
	RebuildCommonClasses (104ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (142ms)
	LoadAllAssembliesAndSetupDomain (7869ms)
		LoadAssemblies (5803ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2090ms)
			TypeCache.Refresh (112ms)
				TypeCache.ScanAssembly (43ms)
			BuildScriptInfoCaches (1623ms)
			ResolveRequiredComponents (323ms)
	FinalizeReload (26141ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (12439ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (47ms)
			SetLoadedEditorAssemblies (124ms)
			BeforeProcessingInitializeOnLoad (1742ms)
			ProcessInitializeOnLoadAttributes (8813ms)
			ProcessInitializeOnLoadMethodAttributes (376ms)
			AfterProcessingInitializeOnLoad (1336ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8220ms)
Refreshing native plugins compatible for Editor in 226.81 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.0 MB). Loaded Objects now: 9028.
Memory consumption went from 317.8 MB to 314.8 MB.
Total: 61.000600 ms (FindLiveObjects: 2.124000 ms CreateObjectMapping: 9.809600 ms MarkObjects: 31.169500 ms  DeleteObjects: 17.895900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 11.140 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 212.80 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.259 seconds
Domain Reload Profiling: 17111ms
	BeginReloadAssembly (1945ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (210ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (228ms)
	RebuildCommonClasses (290ms)
	RebuildNativeTypeToScriptingClass (87ms)
	initialDomainReloadingComplete (342ms)
	LoadAllAssembliesAndSetupDomain (8187ms)
		LoadAssemblies (6584ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2802ms)
			TypeCache.Refresh (92ms)
				TypeCache.ScanAssembly (31ms)
			BuildScriptInfoCaches (2673ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (6261ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4241ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (108ms)
			BeforeProcessingInitializeOnLoad (1191ms)
			ProcessInitializeOnLoadAttributes (2201ms)
			ProcessInitializeOnLoadMethodAttributes (660ms)
			AfterProcessingInitializeOnLoad (63ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (215ms)
Refreshing native plugins compatible for Editor in 3163.26 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (6.3 MB). Loaded Objects now: 9030.
Memory consumption went from 317.8 MB to 311.5 MB.
Total: 97.539100 ms (FindLiveObjects: 1.465900 ms CreateObjectMapping: 7.830300 ms MarkObjects: 63.047400 ms  DeleteObjects: 25.194000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.180 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 139.39 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.613 seconds
Domain Reload Profiling: 14719ms
	BeginReloadAssembly (1357ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (128ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (281ms)
	RebuildCommonClasses (213ms)
	RebuildNativeTypeToScriptingClass (90ms)
	initialDomainReloadingComplete (371ms)
	LoadAllAssembliesAndSetupDomain (5071ms)
		LoadAssemblies (4272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1456ms)
			TypeCache.Refresh (128ms)
				TypeCache.ScanAssembly (60ms)
			BuildScriptInfoCaches (1266ms)
			ResolveRequiredComponents (44ms)
	FinalizeReload (7616ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5800ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (2428ms)
			ProcessInitializeOnLoadAttributes (2854ms)
			ProcessInitializeOnLoadMethodAttributes (441ms)
			AfterProcessingInitializeOnLoad (50ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (130ms)
Refreshing native plugins compatible for Editor in 845.24 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9032.
Memory consumption went from 317.9 MB to 314.9 MB.
Total: 187.628000 ms (FindLiveObjects: 3.799100 ms CreateObjectMapping: 2.330700 ms MarkObjects: 144.768600 ms  DeleteObjects: 36.727400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 11.254 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 33.03 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.686 seconds
Domain Reload Profiling: 17921ms
	BeginReloadAssembly (3196ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (507ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (625ms)
	RebuildCommonClasses (370ms)
	RebuildNativeTypeToScriptingClass (152ms)
	initialDomainReloadingComplete (781ms)
	LoadAllAssembliesAndSetupDomain (6734ms)
		LoadAssemblies (6296ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1611ms)
			TypeCache.Refresh (96ms)
				TypeCache.ScanAssembly (49ms)
			BuildScriptInfoCaches (1421ms)
			ResolveRequiredComponents (71ms)
	FinalizeReload (6689ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4948ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (76ms)
			BeforeProcessingInitializeOnLoad (1565ms)
			ProcessInitializeOnLoadAttributes (2448ms)
			ProcessInitializeOnLoadMethodAttributes (698ms)
			AfterProcessingInitializeOnLoad (147ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (176ms)
Refreshing native plugins compatible for Editor in 27.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9034.
Memory consumption went from 317.8 MB to 315.0 MB.
Total: 3635.321500 ms (FindLiveObjects: 9.178400 ms CreateObjectMapping: 35.511500 ms MarkObjects: 3503.566600 ms  DeleteObjects: 87.063400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.850 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.94 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.894 seconds
Domain Reload Profiling: 7717ms
	BeginReloadAssembly (766ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (69ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (133ms)
	LoadAllAssembliesAndSetupDomain (2825ms)
		LoadAssemblies (2058ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1095ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (1014ms)
			ResolveRequiredComponents (44ms)
	FinalizeReload (3894ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3037ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (652ms)
			ProcessInitializeOnLoadAttributes (1773ms)
			ProcessInitializeOnLoadMethodAttributes (476ms)
			AfterProcessingInitializeOnLoad (118ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (104ms)
Refreshing native plugins compatible for Editor in 630.92 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9036.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 481.253600 ms (FindLiveObjects: 45.500500 ms CreateObjectMapping: 28.762100 ms MarkObjects: 279.020300 ms  DeleteObjects: 127.969100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.177 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 41.77 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.846 seconds
Domain Reload Profiling: 13964ms
	BeginReloadAssembly (1199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (149ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (235ms)
	RebuildCommonClasses (247ms)
	RebuildNativeTypeToScriptingClass (52ms)
	initialDomainReloadingComplete (332ms)
	LoadAllAssembliesAndSetupDomain (6286ms)
		LoadAssemblies (4060ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2788ms)
			TypeCache.Refresh (218ms)
				TypeCache.ScanAssembly (38ms)
			BuildScriptInfoCaches (2442ms)
			ResolveRequiredComponents (107ms)
	FinalizeReload (5848ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4512ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (30ms)
			BeforeProcessingInitializeOnLoad (956ms)
			ProcessInitializeOnLoadAttributes (2677ms)
			ProcessInitializeOnLoadMethodAttributes (681ms)
			AfterProcessingInitializeOnLoad (149ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (201ms)
Refreshing native plugins compatible for Editor in 213.15 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.3 MB). Loaded Objects now: 9038.
Memory consumption went from 317.8 MB to 314.5 MB.
Total: 151.672000 ms (FindLiveObjects: 26.536400 ms CreateObjectMapping: 8.446900 ms MarkObjects: 112.490000 ms  DeleteObjects: 4.197000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 17.993 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 328.08 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 20.267 seconds
Domain Reload Profiling: 38161ms
	BeginReloadAssembly (2673ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (183ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (702ms)
	RebuildCommonClasses (485ms)
	RebuildNativeTypeToScriptingClass (180ms)
	initialDomainReloadingComplete (438ms)
	LoadAllAssembliesAndSetupDomain (14108ms)
		LoadAssemblies (11286ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (4064ms)
			TypeCache.Refresh (95ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (3840ms)
			ResolveRequiredComponents (82ms)
	FinalizeReload (20278ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (10486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (202ms)
			BeforeProcessingInitializeOnLoad (2528ms)
			ProcessInitializeOnLoadAttributes (5195ms)
			ProcessInitializeOnLoadMethodAttributes (2277ms)
			AfterProcessingInitializeOnLoad (280ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (731ms)
Refreshing native plugins compatible for Editor in 3318.70 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.0 MB). Loaded Objects now: 9040.
Memory consumption went from 317.9 MB to 314.9 MB.
Total: 128.800400 ms (FindLiveObjects: 8.707300 ms CreateObjectMapping: 10.976000 ms MarkObjects: 84.087600 ms  DeleteObjects: 25.028000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 17.210 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 118.56 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 22.479 seconds
Domain Reload Profiling: 39262ms
	BeginReloadAssembly (2586ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (435ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (496ms)
	RebuildCommonClasses (384ms)
	RebuildNativeTypeToScriptingClass (68ms)
	initialDomainReloadingComplete (723ms)
	LoadAllAssembliesAndSetupDomain (13019ms)
		LoadAssemblies (10612ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (3511ms)
			TypeCache.Refresh (110ms)
				TypeCache.ScanAssembly (43ms)
			BuildScriptInfoCaches (3168ms)
			ResolveRequiredComponents (118ms)
	FinalizeReload (22481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (8791ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (23ms)
			BeforeProcessingInitializeOnLoad (2697ms)
			ProcessInitializeOnLoadAttributes (4269ms)
			ProcessInitializeOnLoadMethodAttributes (1188ms)
			AfterProcessingInitializeOnLoad (608ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (253ms)
Refreshing native plugins compatible for Editor in 25.19 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9042.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 302.934000 ms (FindLiveObjects: 8.405600 ms CreateObjectMapping: 2.335100 ms MarkObjects: 274.411600 ms  DeleteObjects: 17.779800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.244 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 62.21 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.960 seconds
Domain Reload Profiling: 9137ms
	BeginReloadAssembly (807ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (104ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (161ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (132ms)
	LoadAllAssembliesAndSetupDomain (3127ms)
		LoadAssemblies (2353ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1122ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (994ms)
			ResolveRequiredComponents (64ms)
	FinalizeReload (4961ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3825ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (22ms)
			BeforeProcessingInitializeOnLoad (1151ms)
			ProcessInitializeOnLoadAttributes (1499ms)
			ProcessInitializeOnLoadMethodAttributes (1011ms)
			AfterProcessingInitializeOnLoad (132ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (218ms)
Refreshing native plugins compatible for Editor in 7354.49 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9044.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 1175.213900 ms (FindLiveObjects: 26.106700 ms CreateObjectMapping: 284.213500 ms MarkObjects: 621.900900 ms  DeleteObjects: 242.990900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1950.25 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7857 unused Assets / (2.9 MB). Loaded Objects now: 9044.
Memory consumption went from 317.6 MB to 314.7 MB.
Total: 102.292100 ms (FindLiveObjects: 11.277300 ms CreateObjectMapping: 14.430700 ms MarkObjects: 50.408200 ms  DeleteObjects: 26.174000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.085 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 66.09 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.823 seconds
Domain Reload Profiling: 7832ms
	BeginReloadAssembly (693ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (97ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (107ms)
	RebuildCommonClasses (117ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (155ms)
	LoadAllAssembliesAndSetupDomain (3013ms)
		LoadAssemblies (2563ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (791ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (712ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (3824ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2855ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (25ms)
			BeforeProcessingInitializeOnLoad (1486ms)
			ProcessInitializeOnLoadAttributes (968ms)
			ProcessInitializeOnLoadMethodAttributes (330ms)
			AfterProcessingInitializeOnLoad (42ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (57ms)
Refreshing native plugins compatible for Editor in 4315.26 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9046.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 573.615700 ms (FindLiveObjects: 17.504000 ms CreateObjectMapping: 41.320500 ms MarkObjects: 215.505300 ms  DeleteObjects: 299.283500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.340 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 47.15 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.182 seconds
Domain Reload Profiling: 12471ms
	BeginReloadAssembly (707ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (105ms)
	LoadAllAssembliesAndSetupDomain (3388ms)
		LoadAssemblies (2293ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1409ms)
			TypeCache.Refresh (47ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (1228ms)
			ResolveRequiredComponents (112ms)
	FinalizeReload (8183ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (7133ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (38ms)
			BeforeProcessingInitializeOnLoad (970ms)
			ProcessInitializeOnLoadAttributes (5088ms)
			ProcessInitializeOnLoadMethodAttributes (951ms)
			AfterProcessingInitializeOnLoad (74ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (188ms)
Refreshing native plugins compatible for Editor in 29.84 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9048.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 133.635000 ms (FindLiveObjects: 1.297300 ms CreateObjectMapping: 28.923400 ms MarkObjects: 51.616900 ms  DeleteObjects: 51.795300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.943 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 66.85 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.601 seconds
Domain Reload Profiling: 12408ms
	BeginReloadAssembly (1178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (203ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (132ms)
	RebuildCommonClasses (189ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (189ms)
	LoadAllAssembliesAndSetupDomain (3214ms)
		LoadAssemblies (2753ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1052ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (66ms)
			BuildScriptInfoCaches (898ms)
			ResolveRequiredComponents (48ms)
	FinalizeReload (7601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5611ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (82ms)
			BeforeProcessingInitializeOnLoad (1721ms)
			ProcessInitializeOnLoadAttributes (2738ms)
			ProcessInitializeOnLoadMethodAttributes (805ms)
			AfterProcessingInitializeOnLoad (240ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (353ms)
Refreshing native plugins compatible for Editor in 448.63 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (3.0 MB). Loaded Objects now: 9050.
Memory consumption went from 317.9 MB to 314.9 MB.
Total: 154.001600 ms (FindLiveObjects: 1.604200 ms CreateObjectMapping: 1.690400 ms MarkObjects: 138.271100 ms  DeleteObjects: 12.434300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.818 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 81.07 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.189 seconds
Domain Reload Profiling: 9952ms
	BeginReloadAssembly (1446ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (142ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (100ms)
	RebuildCommonClasses (197ms)
	RebuildNativeTypeToScriptingClass (109ms)
	initialDomainReloadingComplete (143ms)
	LoadAllAssembliesAndSetupDomain (3866ms)
		LoadAssemblies (3169ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1654ms)
			TypeCache.Refresh (67ms)
				TypeCache.ScanAssembly (28ms)
			BuildScriptInfoCaches (1536ms)
			ResolveRequiredComponents (41ms)
	FinalizeReload (4190ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3177ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (936ms)
			ProcessInitializeOnLoadAttributes (1856ms)
			ProcessInitializeOnLoadMethodAttributes (319ms)
			AfterProcessingInitializeOnLoad (45ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (100ms)
Refreshing native plugins compatible for Editor in 90.18 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (4.3 MB). Loaded Objects now: 9052.
Memory consumption went from 317.9 MB to 313.6 MB.
Total: 86.902800 ms (FindLiveObjects: 1.347800 ms CreateObjectMapping: 1.194600 ms MarkObjects: 73.154500 ms  DeleteObjects: 11.204300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.718 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 105.67 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.659 seconds
Domain Reload Profiling: 10294ms
	BeginReloadAssembly (1613ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (176ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (261ms)
	RebuildCommonClasses (180ms)
	RebuildNativeTypeToScriptingClass (86ms)
	initialDomainReloadingComplete (225ms)
	LoadAllAssembliesAndSetupDomain (3530ms)
		LoadAssemblies (3144ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1157ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (17ms)
			BuildScriptInfoCaches (1016ms)
			ResolveRequiredComponents (75ms)
	FinalizeReload (4660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (27ms)
			BeforeProcessingInitializeOnLoad (1079ms)
			ProcessInitializeOnLoadAttributes (1294ms)
			ProcessInitializeOnLoadMethodAttributes (691ms)
			AfterProcessingInitializeOnLoad (246ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (309ms)
Refreshing native plugins compatible for Editor in 1164.09 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9054.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 319.561800 ms (FindLiveObjects: 9.690700 ms CreateObjectMapping: 77.911800 ms MarkObjects: 163.168800 ms  DeleteObjects: 68.788700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 3131.68 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7857 unused Assets / (2.9 MB). Loaded Objects now: 9054.
Memory consumption went from 317.6 MB to 314.7 MB.
Total: 6112.496100 ms (FindLiveObjects: 4.234000 ms CreateObjectMapping: 102.434500 ms MarkObjects: 5827.624100 ms  DeleteObjects: 178.201100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.119 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 101.83 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.120 seconds
Domain Reload Profiling: 12163ms
	BeginReloadAssembly (1906ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (246ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (415ms)
	RebuildCommonClasses (187ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (134ms)
	LoadAllAssembliesAndSetupDomain (4792ms)
		LoadAssemblies (4188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1538ms)
			TypeCache.Refresh (62ms)
				TypeCache.ScanAssembly (22ms)
			BuildScriptInfoCaches (1422ms)
			ResolveRequiredComponents (41ms)
	FinalizeReload (5120ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3937ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (1114ms)
			ProcessInitializeOnLoadAttributes (2064ms)
			ProcessInitializeOnLoadMethodAttributes (643ms)
			AfterProcessingInitializeOnLoad (94ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (184ms)
Refreshing native plugins compatible for Editor in 173.95 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9056.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 91.235300 ms (FindLiveObjects: 2.895500 ms CreateObjectMapping: 9.594300 ms MarkObjects: 66.188100 ms  DeleteObjects: 12.555800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.474 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 85.45 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.160 seconds
Domain Reload Profiling: 10598ms
	BeginReloadAssembly (1043ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (126ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (314ms)
	RebuildCommonClasses (120ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (118ms)
	LoadAllAssembliesAndSetupDomain (2117ms)
		LoadAssemblies (1795ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (719ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (645ms)
			ResolveRequiredComponents (36ms)
	FinalizeReload (7161ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (54ms)
			SetLoadedEditorAssemblies (117ms)
			BeforeProcessingInitializeOnLoad (1037ms)
			ProcessInitializeOnLoadAttributes (3234ms)
			ProcessInitializeOnLoadMethodAttributes (1019ms)
			AfterProcessingInitializeOnLoad (178ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (329ms)
Refreshing native plugins compatible for Editor in 2051.56 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9058.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 815.643500 ms (FindLiveObjects: 1.516400 ms CreateObjectMapping: 1.512700 ms MarkObjects: 635.924200 ms  DeleteObjects: 176.688300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.149 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.24 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.202 seconds
Domain Reload Profiling: 25254ms
	BeginReloadAssembly (1356ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (154ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (264ms)
	RebuildCommonClasses (324ms)
	RebuildNativeTypeToScriptingClass (99ms)
	initialDomainReloadingComplete (407ms)
	LoadAllAssembliesAndSetupDomain (9866ms)
		LoadAssemblies (8767ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1731ms)
			TypeCache.Refresh (49ms)
				TypeCache.ScanAssembly (15ms)
			BuildScriptInfoCaches (1558ms)
			ResolveRequiredComponents (93ms)
	FinalizeReload (13203ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (10077ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (363ms)
			BeforeProcessingInitializeOnLoad (3681ms)
			ProcessInitializeOnLoadAttributes (4850ms)
			ProcessInitializeOnLoadMethodAttributes (1037ms)
			AfterProcessingInitializeOnLoad (126ms)
			EditorAssembliesLoaded (2ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (305ms)
Refreshing native plugins compatible for Editor in 80.12 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (4.8 MB). Loaded Objects now: 9060.
Memory consumption went from 317.9 MB to 313.1 MB.
Total: 122.078900 ms (FindLiveObjects: 1.501300 ms CreateObjectMapping: 4.709900 ms MarkObjects: 92.001700 ms  DeleteObjects: 23.784900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.750 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 74.22 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.305 seconds
Domain Reload Profiling: 7003ms
	BeginReloadAssembly (664ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (87ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (122ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (129ms)
	LoadAllAssembliesAndSetupDomain (2822ms)
		LoadAssemblies (1844ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1280ms)
			TypeCache.Refresh (40ms)
				TypeCache.ScanAssembly (16ms)
			BuildScriptInfoCaches (1139ms)
			ResolveRequiredComponents (74ms)
	FinalizeReload (3305ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2570ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (113ms)
			BeforeProcessingInitializeOnLoad (1089ms)
			ProcessInitializeOnLoadAttributes (1087ms)
			ProcessInitializeOnLoadMethodAttributes (242ms)
			AfterProcessingInitializeOnLoad (36ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (74ms)
Refreshing native plugins compatible for Editor in 81.50 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (2.9 MB). Loaded Objects now: 9062.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 149.912900 ms (FindLiveObjects: 15.887400 ms CreateObjectMapping: 18.808100 ms MarkObjects: 92.637600 ms  DeleteObjects: 22.578000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.386 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 112.06 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.204 seconds
Domain Reload Profiling: 14402ms
	BeginReloadAssembly (1182ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (90ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (193ms)
	RebuildNativeTypeToScriptingClass (65ms)
	initialDomainReloadingComplete (306ms)
	LoadAllAssembliesAndSetupDomain (5450ms)
		LoadAssemblies (4411ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1709ms)
			TypeCache.Refresh (76ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (1521ms)
			ResolveRequiredComponents (87ms)
	FinalizeReload (7205ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4739ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (33ms)
			SetLoadedEditorAssemblies (19ms)
			BeforeProcessingInitializeOnLoad (1758ms)
			ProcessInitializeOnLoadAttributes (2491ms)
			ProcessInitializeOnLoadMethodAttributes (399ms)
			AfterProcessingInitializeOnLoad (39ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (122ms)
Refreshing native plugins compatible for Editor in 31.46 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (7.5 MB). Loaded Objects now: 9064.
Memory consumption went from 317.9 MB to 310.4 MB.
Total: 196.730600 ms (FindLiveObjects: 6.970600 ms CreateObjectMapping: 12.496700 ms MarkObjects: 79.214700 ms  DeleteObjects: 98.046800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.605 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.93 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.799 seconds
Domain Reload Profiling: 13252ms
	BeginReloadAssembly (1667ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (105ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (143ms)
	RebuildCommonClasses (181ms)
	RebuildNativeTypeToScriptingClass (62ms)
	initialDomainReloadingComplete (593ms)
	LoadAllAssembliesAndSetupDomain (5947ms)
		LoadAssemblies (5798ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1175ms)
			TypeCache.Refresh (78ms)
				TypeCache.ScanAssembly (33ms)
			BuildScriptInfoCaches (1067ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (4802ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4043ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (89ms)
			BeforeProcessingInitializeOnLoad (1346ms)
			ProcessInitializeOnLoadAttributes (2105ms)
			ProcessInitializeOnLoadMethodAttributes (388ms)
			AfterProcessingInitializeOnLoad (97ms)
			EditorAssembliesLoaded (5ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (94ms)
Refreshing native plugins compatible for Editor in 46.99 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7907 unused Assets / (5.0 MB). Loaded Objects now: 9066.
Memory consumption went from 317.9 MB to 312.9 MB.
Total: 4623.462800 ms (FindLiveObjects: 579.153600 ms CreateObjectMapping: 653.525400 ms MarkObjects: 3291.569100 ms  DeleteObjects: 99.213100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 14.649 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 83.11 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.104 seconds
Domain Reload Profiling: 20566ms
	BeginReloadAssembly (2656ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (214ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (511ms)
	RebuildCommonClasses (331ms)
	RebuildNativeTypeToScriptingClass (54ms)
	initialDomainReloadingComplete (213ms)
	LoadAllAssembliesAndSetupDomain (11208ms)
		LoadAssemblies (10763ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1904ms)
			TypeCache.Refresh (39ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (1817ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (6105ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5169ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (25ms)
			BeforeProcessingInitializeOnLoad (896ms)
			ProcessInitializeOnLoadAttributes (3253ms)
			ProcessInitializeOnLoadMethodAttributes (867ms)
			AfterProcessingInitializeOnLoad (122ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (160ms)
Refreshing native plugins compatible for Editor in 126.30 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.3 MB). Loaded Objects now: 9069.
Memory consumption went from 317.9 MB to 312.7 MB.
Total: 268.453900 ms (FindLiveObjects: 23.728700 ms CreateObjectMapping: 37.964500 ms MarkObjects: 141.713300 ms  DeleteObjects: 65.033800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.458 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 133.38 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.917 seconds
Domain Reload Profiling: 9330ms
	BeginReloadAssembly (947ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (165ms)
	RebuildCommonClasses (109ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (124ms)
	LoadAllAssembliesAndSetupDomain (2208ms)
		LoadAssemblies (1895ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (705ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (610ms)
			ResolveRequiredComponents (55ms)
	FinalizeReload (5919ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (62ms)
			BeforeProcessingInitializeOnLoad (2118ms)
			ProcessInitializeOnLoadAttributes (1804ms)
			ProcessInitializeOnLoadMethodAttributes (425ms)
			AfterProcessingInitializeOnLoad (79ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (109ms)
Refreshing native plugins compatible for Editor in 424.43 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (8.8 MB). Loaded Objects now: 9071.
Memory consumption went from 317.9 MB to 309.2 MB.
Total: 261.513900 ms (FindLiveObjects: 8.881200 ms CreateObjectMapping: 9.623700 ms MarkObjects: 57.269300 ms  DeleteObjects: 185.737800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Error loading the file 'Library/BuildProfileContext.asset'. File is either empty or corrupted, please verify the file contents.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForgetInternal (string,UnityEngine.Object[],bool)
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForget (UnityEngine.Object[],string,bool)
UnityEditor.Build.Profile.BuildProfileContext:Save ()
UnityEditor.Build.Profile.BuildProfileContext:OnDisable ()

[ line 0]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.598 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 60.62 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.904 seconds
Domain Reload Profiling: 9468ms
	BeginReloadAssembly (1414ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (167ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (302ms)
	RebuildCommonClasses (131ms)
	RebuildNativeTypeToScriptingClass (78ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (3812ms)
		LoadAssemblies (3142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1258ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (10ms)
			BuildScriptInfoCaches (1155ms)
			ResolveRequiredComponents (45ms)
	FinalizeReload (3905ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3021ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (68ms)
			BeforeProcessingInitializeOnLoad (831ms)
			ProcessInitializeOnLoadAttributes (1721ms)
			ProcessInitializeOnLoadMethodAttributes (350ms)
			AfterProcessingInitializeOnLoad (42ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (86ms)
Refreshing native plugins compatible for Editor in 39.98 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (7.2 MB). Loaded Objects now: 9073.
Memory consumption went from 317.9 MB to 310.7 MB.
Total: 664.397300 ms (FindLiveObjects: 19.558500 ms CreateObjectMapping: 28.571300 ms MarkObjects: 268.748700 ms  DeleteObjects: 347.517300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.653 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 37.32 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.855 seconds
Domain Reload Profiling: 8408ms
	BeginReloadAssembly (976ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (127ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (152ms)
	RebuildCommonClasses (116ms)
	RebuildNativeTypeToScriptingClass (48ms)
	initialDomainReloadingComplete (247ms)
	LoadAllAssembliesAndSetupDomain (3165ms)
		LoadAssemblies (2533ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1127ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (976ms)
			ResolveRequiredComponents (100ms)
	FinalizeReload (3856ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3038ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (854ms)
			ProcessInitializeOnLoadAttributes (1619ms)
			ProcessInitializeOnLoadMethodAttributes (489ms)
			AfterProcessingInitializeOnLoad (61ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (196ms)
Refreshing native plugins compatible for Editor in 703.24 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (8.5 MB). Loaded Objects now: 9075.
Memory consumption went from 317.9 MB to 309.4 MB.
Total: 2188.563900 ms (FindLiveObjects: 29.707400 ms CreateObjectMapping: 71.763900 ms MarkObjects: 57.690700 ms  DeleteObjects: 2029.399700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.941 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 65.49 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.609 seconds
Domain Reload Profiling: 11524ms
	BeginReloadAssembly (1199ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (120ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (5ms)
		CreateAndSetChildDomain (265ms)
	RebuildCommonClasses (138ms)
	RebuildNativeTypeToScriptingClass (78ms)
	initialDomainReloadingComplete (378ms)
	LoadAllAssembliesAndSetupDomain (5119ms)
		LoadAssemblies (2700ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2749ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (13ms)
			BuildScriptInfoCaches (2381ms)
			ResolveRequiredComponents (228ms)
	FinalizeReload (4612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3626ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (1060ms)
			ProcessInitializeOnLoadAttributes (1952ms)
			ProcessInitializeOnLoadMethodAttributes (474ms)
			AfterProcessingInitializeOnLoad (85ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (107ms)
Refreshing native plugins compatible for Editor in 39.64 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9077.
Memory consumption went from 317.9 MB to 315.1 MB.
Total: 38.131500 ms (FindLiveObjects: 1.361000 ms CreateObjectMapping: 2.878600 ms MarkObjects: 20.312200 ms  DeleteObjects: 13.578200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 51.97 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (5.0 MB). Loaded Objects now: 9077.
Memory consumption went from 317.6 MB to 312.7 MB.
Total: 85.580900 ms (FindLiveObjects: 1.464900 ms CreateObjectMapping: 1.523200 ms MarkObjects: 67.568000 ms  DeleteObjects: 15.023200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  6.016 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 74.11 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.445 seconds
Domain Reload Profiling: 10354ms
	BeginReloadAssembly (819ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (132ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (91ms)
	RebuildNativeTypeToScriptingClass (50ms)
	initialDomainReloadingComplete (203ms)
	LoadAllAssembliesAndSetupDomain (4745ms)
		LoadAssemblies (4222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (907ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (832ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (4446ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3368ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (34ms)
			BeforeProcessingInitializeOnLoad (1179ms)
			ProcessInitializeOnLoadAttributes (1718ms)
			ProcessInitializeOnLoadMethodAttributes (362ms)
			AfterProcessingInitializeOnLoad (73ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (3ms)
		AwakeInstancesAfterBackupRestoration (75ms)
Refreshing native plugins compatible for Editor in 55.03 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9079.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 6543.100100 ms (FindLiveObjects: 163.553700 ms CreateObjectMapping: 839.018400 ms MarkObjects: 5522.236100 ms  DeleteObjects: 18.290000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.942 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 59.71 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.850 seconds
Domain Reload Profiling: 9759ms
	BeginReloadAssembly (1243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (107ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (200ms)
	RebuildCommonClasses (111ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (131ms)
	LoadAllAssembliesAndSetupDomain (3393ms)
		LoadAssemblies (2496ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1407ms)
			TypeCache.Refresh (75ms)
				TypeCache.ScanAssembly (29ms)
			BuildScriptInfoCaches (1262ms)
			ResolveRequiredComponents (51ms)
	FinalizeReload (4851ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3531ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (922ms)
			ProcessInitializeOnLoadAttributes (1612ms)
			ProcessInitializeOnLoadMethodAttributes (711ms)
			AfterProcessingInitializeOnLoad (270ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (399ms)
Refreshing native plugins compatible for Editor in 360.75 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9081.
Memory consumption went from 317.9 MB to 315.1 MB.
Total: 121.136100 ms (FindLiveObjects: 5.562300 ms CreateObjectMapping: 3.506200 ms MarkObjects: 71.691700 ms  DeleteObjects: 40.374500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 12.094 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 90.32 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 10.216 seconds
Domain Reload Profiling: 22250ms
	BeginReloadAssembly (3325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (252ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (451ms)
	RebuildCommonClasses (374ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (189ms)
	LoadAllAssembliesAndSetupDomain (8106ms)
		LoadAssemblies (7636ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2431ms)
			TypeCache.Refresh (56ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (2246ms)
			ResolveRequiredComponents (90ms)
	FinalizeReload (10217ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (8086ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (55ms)
			BeforeProcessingInitializeOnLoad (2029ms)
			ProcessInitializeOnLoadAttributes (4544ms)
			ProcessInitializeOnLoadMethodAttributes (834ms)
			AfterProcessingInitializeOnLoad (561ms)
			EditorAssembliesLoaded (28ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (329ms)
Refreshing native plugins compatible for Editor in 11012.16 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9083.
Memory consumption went from 317.9 MB to 315.1 MB.
Total: 253.751700 ms (FindLiveObjects: 7.037900 ms CreateObjectMapping: 95.567200 ms MarkObjects: 140.462500 ms  DeleteObjects: 10.681800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.765 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.77 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.664 seconds
Domain Reload Profiling: 7355ms
	BeginReloadAssembly (679ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (104ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (104ms)
	RebuildCommonClasses (93ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (134ms)
	LoadAllAssembliesAndSetupDomain (1755ms)
		LoadAssemblies (1467ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (552ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (496ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (4665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3335ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (843ms)
			ProcessInitializeOnLoadAttributes (1885ms)
			ProcessInitializeOnLoadMethodAttributes (507ms)
			AfterProcessingInitializeOnLoad (81ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (287ms)
Refreshing native plugins compatible for Editor in 225.98 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.7 MB). Loaded Objects now: 9085.
Memory consumption went from 317.9 MB to 313.2 MB.
Total: 108.765600 ms (FindLiveObjects: 24.153700 ms CreateObjectMapping: 10.830100 ms MarkObjects: 63.688700 ms  DeleteObjects: 10.091300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.454 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 118.17 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.031 seconds
Domain Reload Profiling: 8487ms
	BeginReloadAssembly (754ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (93ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (116ms)
	RebuildCommonClasses (100ms)
	RebuildNativeTypeToScriptingClass (61ms)
	initialDomainReloadingComplete (185ms)
	LoadAllAssembliesAndSetupDomain (2355ms)
		LoadAssemblies (1638ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1074ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (928ms)
			ResolveRequiredComponents (92ms)
	FinalizeReload (5033ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (1241ms)
			ProcessInitializeOnLoadAttributes (1806ms)
			ProcessInitializeOnLoadMethodAttributes (390ms)
			AfterProcessingInitializeOnLoad (71ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (112ms)
Refreshing native plugins compatible for Editor in 1142.82 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9087.
Memory consumption went from 317.9 MB to 315.1 MB.
Total: 223.693700 ms (FindLiveObjects: 7.757300 ms CreateObjectMapping: 23.009200 ms MarkObjects: 163.751000 ms  DeleteObjects: 29.175000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.51 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (3.1 MB). Loaded Objects now: 9087.
Memory consumption went from 317.6 MB to 314.5 MB.
Total: 452.036800 ms (FindLiveObjects: 2.312500 ms CreateObjectMapping: 2.118500 ms MarkObjects: 442.875200 ms  DeleteObjects: 4.728800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  9.554 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.86 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.695 seconds
Domain Reload Profiling: 14079ms
	BeginReloadAssembly (1000ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (144ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (200ms)
	RebuildCommonClasses (233ms)
	RebuildNativeTypeToScriptingClass (59ms)
	initialDomainReloadingComplete (174ms)
	LoadAllAssembliesAndSetupDomain (7917ms)
		LoadAssemblies (7317ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1080ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (986ms)
			ResolveRequiredComponents (52ms)
	FinalizeReload (4696ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3905ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (90ms)
			BeforeProcessingInitializeOnLoad (1732ms)
			ProcessInitializeOnLoadAttributes (1638ms)
			ProcessInitializeOnLoadMethodAttributes (386ms)
			AfterProcessingInitializeOnLoad (54ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (101ms)
Refreshing native plugins compatible for Editor in 71.13 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.7 MB). Loaded Objects now: 9089.
Memory consumption went from 317.9 MB to 314.2 MB.
Total: 135.492100 ms (FindLiveObjects: 29.731500 ms CreateObjectMapping: 36.794800 ms MarkObjects: 56.820900 ms  DeleteObjects: 12.143300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.279 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.06 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.392 seconds
Domain Reload Profiling: 12632ms
	BeginReloadAssembly (2207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (345ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (229ms)
	RebuildCommonClasses (190ms)
	RebuildNativeTypeToScriptingClass (58ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (2670ms)
		LoadAssemblies (2350ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1283ms)
			TypeCache.Refresh (30ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (1157ms)
			ResolveRequiredComponents (74ms)
	FinalizeReload (7393ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5957ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (1241ms)
			ProcessInitializeOnLoadAttributes (2925ms)
			ProcessInitializeOnLoadMethodAttributes (1307ms)
			AfterProcessingInitializeOnLoad (426ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (88ms)
Refreshing native plugins compatible for Editor in 1276.00 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (8.0 MB). Loaded Objects now: 9091.
Memory consumption went from 318.0 MB to 310.0 MB.
Total: 193.832600 ms (FindLiveObjects: 19.524500 ms CreateObjectMapping: 23.669400 ms MarkObjects: 77.131200 ms  DeleteObjects: 73.426200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 64924.290564 seconds.
  path: Assets/_Game/Scripts/DeathSys/DeathManager.cs
  artifactKey: Guid(1f1d528f5fabde04789866fa7dc95b34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/DeathSys/DeathManager.cs using Guid(1f1d528f5fabde04789866fa7dc95b34) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15f4533f28415bce943e16c81b97c5b6') in 4.1987794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.284 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 45.09 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.039 seconds
Domain Reload Profiling: 10277ms
	BeginReloadAssembly (1165ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (50ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (189ms)
	RebuildCommonClasses (104ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (121ms)
	LoadAllAssembliesAndSetupDomain (2817ms)
		LoadAssemblies (2359ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1132ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (19ms)
			BuildScriptInfoCaches (1020ms)
			ResolveRequiredComponents (50ms)
	FinalizeReload (6040ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4768ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (59ms)
			BeforeProcessingInitializeOnLoad (2317ms)
			ProcessInitializeOnLoadAttributes (1774ms)
			ProcessInitializeOnLoadMethodAttributes (553ms)
			AfterProcessingInitializeOnLoad (61ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (261ms)
Refreshing native plugins compatible for Editor in 23.71 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.6 MB). Loaded Objects now: 9093.
Memory consumption went from 318.0 MB to 314.3 MB.
Total: 1758.916400 ms (FindLiveObjects: 257.318500 ms CreateObjectMapping: 1441.640700 ms MarkObjects: 52.597500 ms  DeleteObjects: 7.358000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.592 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 89.76 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.703 seconds
Domain Reload Profiling: 9240ms
	BeginReloadAssembly (1183ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (152ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (144ms)
	RebuildCommonClasses (123ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (163ms)
	LoadAllAssembliesAndSetupDomain (3042ms)
		LoadAssemblies (2466ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1157ms)
			TypeCache.Refresh (34ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (951ms)
			ResolveRequiredComponents (156ms)
	FinalizeReload (4705ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3511ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (879ms)
			ProcessInitializeOnLoadAttributes (1658ms)
			ProcessInitializeOnLoadMethodAttributes (808ms)
			AfterProcessingInitializeOnLoad (143ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (139ms)
Refreshing native plugins compatible for Editor in 103.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9095.
Memory consumption went from 318.0 MB to 315.1 MB.
Total: 429.824900 ms (FindLiveObjects: 2.069800 ms CreateObjectMapping: 1.381800 ms MarkObjects: 90.335900 ms  DeleteObjects: 336.035800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.239 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 46.51 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.480 seconds
Domain Reload Profiling: 6693ms
	BeginReloadAssembly (1128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (101ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (194ms)
	RebuildCommonClasses (121ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (1835ms)
		LoadAssemblies (1763ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (614ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (494ms)
			ResolveRequiredComponents (77ms)
	FinalizeReload (3480ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2636ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (46ms)
			BeforeProcessingInitializeOnLoad (667ms)
			ProcessInitializeOnLoadAttributes (1541ms)
			ProcessInitializeOnLoadMethodAttributes (338ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (70ms)
Refreshing native plugins compatible for Editor in 118.17 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9097.
Memory consumption went from 318.0 MB to 315.1 MB.
Total: 38.523900 ms (FindLiveObjects: 3.835300 ms CreateObjectMapping: 4.249400 ms MarkObjects: 24.517300 ms  DeleteObjects: 5.920500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.163 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.71 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.621 seconds
Domain Reload Profiling: 5745ms
	BeginReloadAssembly (1045ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (172ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (180ms)
	RebuildCommonClasses (78ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (95ms)
	LoadAllAssembliesAndSetupDomain (1878ms)
		LoadAssemblies (1592ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (809ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (19ms)
			BuildScriptInfoCaches (716ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (2622ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1984ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (26ms)
			BeforeProcessingInitializeOnLoad (634ms)
			ProcessInitializeOnLoadAttributes (959ms)
			ProcessInitializeOnLoadMethodAttributes (323ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (63ms)
Refreshing native plugins compatible for Editor in 243.34 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (9.5 MB). Loaded Objects now: 9099.
Memory consumption went from 318.0 MB to 308.4 MB.
Total: 650.002700 ms (FindLiveObjects: 1.364900 ms CreateObjectMapping: 11.660000 ms MarkObjects: 186.903400 ms  DeleteObjects: 450.071800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 234.13 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (3.5 MB). Loaded Objects now: 9099.
Memory consumption went from 317.7 MB to 314.2 MB.
Total: 65.571200 ms (FindLiveObjects: 1.615100 ms CreateObjectMapping: 1.736200 ms MarkObjects: 55.647700 ms  DeleteObjects: 6.570000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.551 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 70.45 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.961 seconds
Domain Reload Profiling: 13480ms
	BeginReloadAssembly (1128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (252ms)
	RebuildCommonClasses (129ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (4126ms)
		LoadAssemblies (2896ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1743ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (29ms)
			BuildScriptInfoCaches (1546ms)
			ResolveRequiredComponents (83ms)
	FinalizeReload (7962ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6538ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (1467ms)
			ProcessInitializeOnLoadAttributes (3666ms)
			ProcessInitializeOnLoadMethodAttributes (1292ms)
			AfterProcessingInitializeOnLoad (93ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (322ms)
Refreshing native plugins compatible for Editor in 2879.38 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (7.2 MB). Loaded Objects now: 9101.
Memory consumption went from 318.0 MB to 310.7 MB.
Total: 347.942700 ms (FindLiveObjects: 9.400600 ms CreateObjectMapping: 4.882300 ms MarkObjects: 304.439400 ms  DeleteObjects: 29.218800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.390 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 108.81 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  5.050 seconds
Domain Reload Profiling: 9396ms
	BeginReloadAssembly (1573ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (369ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (449ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (99ms)
	LoadAllAssembliesAndSetupDomain (2597ms)
		LoadAssemblies (2008ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (905ms)
			TypeCache.Refresh (42ms)
				TypeCache.ScanAssembly (20ms)
			BuildScriptInfoCaches (814ms)
			ResolveRequiredComponents (35ms)
	FinalizeReload (5051ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3836ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (18ms)
			BeforeProcessingInitializeOnLoad (995ms)
			ProcessInitializeOnLoadAttributes (2011ms)
			ProcessInitializeOnLoadMethodAttributes (525ms)
			AfterProcessingInitializeOnLoad (283ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (216ms)
Refreshing native plugins compatible for Editor in 1013.45 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9103.
Memory consumption went from 318.0 MB to 315.1 MB.
Total: 1123.360500 ms (FindLiveObjects: 10.706400 ms CreateObjectMapping: 9.170300 ms MarkObjects: 33.499600 ms  DeleteObjects: 1069.982400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 11899.036116 seconds.
  path: Assets/_Game/Scenes/Main/Abyss Volume Profile 1.asset
  artifactKey: Guid(75ed8b3cc87e37e43a4d5f2f0668b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/Abyss Volume Profile 1.asset using Guid(75ed8b3cc87e37e43a4d5f2f0668b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4596ceb9fb6a1339c669ef2e288f37a7') in 0.7689316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 8.906843 seconds.
  path: Assets/_Game/Scenes/Main/StartWorld Volume.asset
  artifactKey: Guid(75ed8b3cc87e37e43a4d5f2f0668b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main/StartWorld Volume.asset using Guid(75ed8b3cc87e37e43a4d5f2f0668b02f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a030ddf02242f2ed5cf89bee0ce637a') in 0.0066462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.500 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.87 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.510 seconds
Domain Reload Profiling: 7902ms
	BeginReloadAssembly (1803ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (448ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (487ms)
	RebuildCommonClasses (138ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (230ms)
	LoadAllAssembliesAndSetupDomain (2188ms)
		LoadAssemblies (1872ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (672ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (616ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (3510ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2789ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (425ms)
			ProcessInitializeOnLoadAttributes (2005ms)
			ProcessInitializeOnLoadMethodAttributes (311ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (178ms)
Refreshing native plugins compatible for Editor in 302.68 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9105.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 154.395700 ms (FindLiveObjects: 1.981400 ms CreateObjectMapping: 26.861800 ms MarkObjects: 121.901200 ms  DeleteObjects: 3.649500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  8.803 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.59 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.744 seconds
Domain Reload Profiling: 16435ms
	BeginReloadAssembly (3412ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (808ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (658ms)
	RebuildCommonClasses (155ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (187ms)
	LoadAllAssembliesAndSetupDomain (4915ms)
		LoadAssemblies (4866ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1169ms)
			TypeCache.Refresh (50ms)
				TypeCache.ScanAssembly (14ms)
			BuildScriptInfoCaches (1024ms)
			ResolveRequiredComponents (65ms)
	FinalizeReload (7747ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (1011ms)
			ProcessInitializeOnLoadAttributes (4519ms)
			ProcessInitializeOnLoadMethodAttributes (943ms)
			AfterProcessingInitializeOnLoad (67ms)
			EditorAssembliesLoaded (4ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (199ms)
Refreshing native plugins compatible for Editor in 270.39 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.1 MB). Loaded Objects now: 9107.
Memory consumption went from 318.1 MB to 315.0 MB.
Total: 48.333300 ms (FindLiveObjects: 10.778000 ms CreateObjectMapping: 6.253500 ms MarkObjects: 24.671000 ms  DeleteObjects: 6.628900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.903 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 40.94 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.188 seconds
Domain Reload Profiling: 6995ms
	BeginReloadAssembly (1155ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (252ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (142ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (171ms)
	LoadAllAssembliesAndSetupDomain (3304ms)
		LoadAssemblies (2947ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (956ms)
			TypeCache.Refresh (41ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (882ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (2189ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1639ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (613ms)
			ProcessInitializeOnLoadAttributes (797ms)
			ProcessInitializeOnLoadMethodAttributes (186ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 26.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.1 MB). Loaded Objects now: 9109.
Memory consumption went from 318.1 MB to 315.0 MB.
Total: 759.086800 ms (FindLiveObjects: 90.708100 ms CreateObjectMapping: 61.610600 ms MarkObjects: 596.808300 ms  DeleteObjects: 9.945300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.632 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 43.44 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.874 seconds
Domain Reload Profiling: 9405ms
	BeginReloadAssembly (1232ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (230ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (171ms)
	RebuildCommonClasses (221ms)
	RebuildNativeTypeToScriptingClass (61ms)
	initialDomainReloadingComplete (186ms)
	LoadAllAssembliesAndSetupDomain (2830ms)
		LoadAssemblies (2148ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1208ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (1116ms)
			ResolveRequiredComponents (55ms)
	FinalizeReload (4875ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3687ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (54ms)
			BeforeProcessingInitializeOnLoad (1059ms)
			ProcessInitializeOnLoadAttributes (2039ms)
			ProcessInitializeOnLoadMethodAttributes (439ms)
			AfterProcessingInitializeOnLoad (76ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (149ms)
Refreshing native plugins compatible for Editor in 82.89 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9111.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 636.126200 ms (FindLiveObjects: 191.916300 ms CreateObjectMapping: 157.416800 ms MarkObjects: 265.595400 ms  DeleteObjects: 21.195800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  7.931 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 53.97 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.701 seconds
Domain Reload Profiling: 12544ms
	BeginReloadAssembly (1902ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (157ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (642ms)
	RebuildCommonClasses (231ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (159ms)
	LoadAllAssembliesAndSetupDomain (5521ms)
		LoadAssemblies (5018ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1278ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (1181ms)
			ResolveRequiredComponents (60ms)
	FinalizeReload (4702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (4176ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (29ms)
			BeforeProcessingInitializeOnLoad (1442ms)
			ProcessInitializeOnLoadAttributes (2362ms)
			ProcessInitializeOnLoadMethodAttributes (303ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (58ms)
Refreshing native plugins compatible for Editor in 50.20 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9113.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 38.765300 ms (FindLiveObjects: 7.085800 ms CreateObjectMapping: 1.552900 ms MarkObjects: 26.418100 ms  DeleteObjects: 3.705200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 33018.078018 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'da6cd623539043fab7b04fb783ffca21') in 0.6865929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 534.582078 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '18cfc15e0258c4a2650ca36ab4333797') in 0.0190984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 493.101002 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '34354f0d857f4bd3ae794a95f95614e2') in 0.0096722 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 90.588789 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '2308153d380145aebee90e95d9f36bb8') in 0.0227448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 347.448291 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'ca0203d301e7a5d6059c0ae76646f99e') in 0.0173249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 91.520875 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '3cc45350b72bd631899325e779b3e402') in 0.017107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 59.554034 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/inve.uxml
  artifactKey: Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/inve.uxml using Guid(12c8171353d210a4488f806368fcb07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '272b2b468759c190cb747f89855f782c') in 0.0440708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 58741.954072 seconds.
  path: Assets/_Game/Scripts/DeathSys/DeathManager.cs
  artifactKey: Guid(1f1d528f5fabde04789866fa7dc95b34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/DeathSys/DeathManager.cs using Guid(1f1d528f5fabde04789866fa7dc95b34) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67c35892aad8730995d79832bbbdb1aa') in 0.014354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 10.191 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 72.70 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.530 seconds
Domain Reload Profiling: 14686ms
	BeginReloadAssembly (2863ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (210ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (840ms)
	RebuildCommonClasses (647ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (148ms)
	LoadAllAssembliesAndSetupDomain (6463ms)
		LoadAssemblies (5985ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1631ms)
			TypeCache.Refresh (67ms)
				TypeCache.ScanAssembly (18ms)
			BuildScriptInfoCaches (1510ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (4531ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (3522ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (38ms)
			BeforeProcessingInitializeOnLoad (725ms)
			ProcessInitializeOnLoadAttributes (2270ms)
			ProcessInitializeOnLoadMethodAttributes (415ms)
			AfterProcessingInitializeOnLoad (70ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (132ms)
Refreshing native plugins compatible for Editor in 105.14 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.1 MB). Loaded Objects now: 9115.
Memory consumption went from 318.1 MB to 314.0 MB.
Total: 70.171100 ms (FindLiveObjects: 11.584000 ms CreateObjectMapping: 2.806400 ms MarkObjects: 34.771700 ms  DeleteObjects: 21.007600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.521 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.84 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  8.220 seconds
Domain Reload Profiling: 13699ms
	BeginReloadAssembly (1657ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (407ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (444ms)
	RebuildCommonClasses (140ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (120ms)
	LoadAllAssembliesAndSetupDomain (3532ms)
		LoadAssemblies (2876ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1154ms)
			TypeCache.Refresh (73ms)
				TypeCache.ScanAssembly (12ms)
			BuildScriptInfoCaches (1033ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (8221ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6365ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (1291ms)
			ProcessInitializeOnLoadAttributes (3061ms)
			ProcessInitializeOnLoadMethodAttributes (1623ms)
			AfterProcessingInitializeOnLoad (377ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (711ms)
Refreshing native plugins compatible for Editor in 2043.11 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9117.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 276.495900 ms (FindLiveObjects: 3.590300 ms CreateObjectMapping: 10.837600 ms MarkObjects: 194.836800 ms  DeleteObjects: 67.229700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 14858.323654 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml
  artifactKey: Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml using Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'b1d7e7ab144bd8c3f499c4b06505ab41') in 0.5530002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/SettingsSubMenu/SettingsMenu.uxml
  artifactKey: Guid(1a74ddd0cdb678b4c84098839a487e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/SettingsSubMenu/SettingsMenu.uxml using Guid(1a74ddd0cdb678b4c84098839a487e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bcedc3eeaccc06dc0018043eefd88769') in 0.0229802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml
  artifactKey: Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml using Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'b72219235e591023d2cda1c661324760') in 0.011666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.297 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.99 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.504 seconds
Domain Reload Profiling: 6754ms
	BeginReloadAssembly (1141ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (110ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (157ms)
	RebuildCommonClasses (164ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (99ms)
	LoadAllAssembliesAndSetupDomain (2818ms)
		LoadAssemblies (2693ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (815ms)
			TypeCache.Refresh (55ms)
				TypeCache.ScanAssembly (13ms)
			BuildScriptInfoCaches (720ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (2504ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1990ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (771ms)
			ProcessInitializeOnLoadAttributes (950ms)
			ProcessInitializeOnLoadMethodAttributes (225ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (65ms)
Refreshing native plugins compatible for Editor in 133.96 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9119.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 494.446600 ms (FindLiveObjects: 23.281200 ms CreateObjectMapping: 12.406300 ms MarkObjects: 404.007600 ms  DeleteObjects: 54.740100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.695 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 45.04 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.344 seconds
Domain Reload Profiling: 11951ms
	BeginReloadAssembly (1532ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (165ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (335ms)
	RebuildCommonClasses (126ms)
	RebuildNativeTypeToScriptingClass (42ms)
	initialDomainReloadingComplete (201ms)
	LoadAllAssembliesAndSetupDomain (2706ms)
		LoadAssemblies (2632ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (668ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (593ms)
			ResolveRequiredComponents (40ms)
	FinalizeReload (7345ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (35ms)
			BeforeProcessingInitializeOnLoad (1346ms)
			ProcessInitializeOnLoadAttributes (4466ms)
			ProcessInitializeOnLoadMethodAttributes (446ms)
			AfterProcessingInitializeOnLoad (72ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (181ms)
Refreshing native plugins compatible for Editor in 185.26 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9121.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 115.541900 ms (FindLiveObjects: 14.703800 ms CreateObjectMapping: 19.905500 ms MarkObjects: 76.553300 ms  DeleteObjects: 4.377400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  5.025 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.86 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.301 seconds
Domain Reload Profiling: 6302ms
	BeginReloadAssembly (910ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (203ms)
	RebuildCommonClasses (173ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (3831ms)
		LoadAssemblies (3444ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (846ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (790ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1303ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (956ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (244ms)
			ProcessInitializeOnLoadAttributes (547ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 27.01 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.8 MB). Loaded Objects now: 9123.
Memory consumption went from 318.1 MB to 312.3 MB.
Total: 19.485000 ms (FindLiveObjects: 1.546900 ms CreateObjectMapping: 1.288200 ms MarkObjects: 11.467700 ms  DeleteObjects: 5.179400 ms)

Prepare: number of updated asset objects reloaded= 0
