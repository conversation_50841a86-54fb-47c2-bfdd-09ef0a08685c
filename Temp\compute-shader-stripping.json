{"totalVariantsIn": 16286, "totalVariantsOut": 2244, "shaders": [{"inputVariants": 4, "outputVariants": 0, "name": "StageSetupSegment", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 7.2041}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.040100000000000004}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "DepthOfFieldCoC", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.051000000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.0533}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0267}]}]}, {"inputVariants": 720, "outputVariants": 80, "name": "WaterLighting", "pipelines": [{"inputVariants": 720, "outputVariants": 80, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.22890000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.22390000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.1865}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.2257}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.1688}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.1809}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.1645}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.2101}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.1778}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.1756}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.1814}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.18430000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.24000000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.198}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.25930000000000003}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.20650000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.1925}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.2379}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.22310000000000002}, {"inputVariants": 36, "outputVariants": 4, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.2381}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "LensFlareMergeOcclusionDataDriven", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0238}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.006900000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "OccluderDepthPyramidKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.061900000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.07930000000000001}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ProbeVolumeUploadData", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.1051}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.09680000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BakeCloudTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.015000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0078000000000000005}]}]}, {"inputVariants": 13392, "outputVariants": 372, "name": "Deferred", "pipelines": [{"inputVariants": 13392, "outputVariants": 372, "pipeline": "", "variants": [{"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 1.3994}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.9048}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 1.4239000000000002}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 1.1681000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 1.5746}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 1.387}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 1.3627}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 1.0563}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 1.2149}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 1.114}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 1.0385}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.9235000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 1.1855}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 1.2207000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 1.2458}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 1.1866}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 1.1874}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 1.0767}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 1.1039}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 1.0649}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 1.231}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 1.2408000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 1.179}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 1.3028}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 1.0574000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.9810000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 8.3751}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 2.3892}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 3.4336}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 2.1789}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 1.52}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 1.6399000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 2.9931}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 3.0408}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 3.7028000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 2.9527}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 2.8036000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 2.3065}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 2.7261}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 2.1926}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 2.2948}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 2.3262}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 3.0172000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 3.7377000000000002}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 3.3208}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 2.9298}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 2.3850000000000002}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 2.0378000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 2.2427}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 2.2007000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 2.0135}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 1.9137000000000002}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 1.8844}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 1.8196}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 2.1367000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 2.9113}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 2.5909}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 2.2169000000000003}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 2.4758}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 2.3713}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 1.0915000000000001}, {"inputVariants": 216, "outputVariants": 6, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 1.2081}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "lightlistbuild-clustered", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.0239}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0485}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0167}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "WaterFoam", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0135}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PaniniProjection", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0442}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0132}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0376}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "FXAA", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.037000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "StageRasterBin", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0017000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ApplyExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.010100000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0351}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldCoCDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "lightlistbuild-clearatomic", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0303}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0606}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "WaterLine", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HistogramExposure", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0131}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0086}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0076}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0016}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "VFXCopyBuffer", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.008}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.04}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0167}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0152}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BloomPrefilter", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.029300000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VolumetricMaterial", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.0089}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ScreenSpaceMultipleScattering", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0085}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "InstanceTransformUpdateKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.012100000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0234}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0089}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.0528}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.0085}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0066}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.014700000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.008700000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOSpatialDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.014400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.01}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldMipSafe", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0158}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.010100000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOTemporalDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0125}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.1092}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "WaterSimulation", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.016900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DownsampleVTFeedback", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0109}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurGenTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.051800000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 64, "outputVariants": 64, "name": "StpSetup", "pipelines": [{"inputVariants": 64, "outputVariants": 64, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.1791}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.309}]}]}, {"inputVariants": 48, "outputVariants": 24, "name": "LutBuilder3D", "pipelines": [{"inputVariants": 48, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.215}, {"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.23190000000000002}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.09680000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0869}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldPreCombineFar", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.012400000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ContactShadows", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0175}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0081}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "BakeCloudShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.09730000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.0853}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "DepthOfFieldGather", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0641}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0313}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0653}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.030100000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldCoCReproject", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0632}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0079}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HairMultipleScatteringPreIntegration", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0097}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "BlitAndExpose", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.049800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.004}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "MomentShadows", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ColorPyramid", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.011000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0204}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.038}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "builddispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RandomDownsample", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0082}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHistogramImage", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0077}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0038}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StagePrepare", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0067}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0442}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0016}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferUploadKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0076}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ContrastAdaptiveSharpen", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0171}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0119}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0117}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0115}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0094}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurNeighborhoodTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.011300000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0218}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "lightlistbuild-bigtile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.0122}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.0073}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ProbeVolumeUploadDataL2", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugWaveform", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.038400000000000004}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthPyramid", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0117}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.009000000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "lightlistbuild", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.0695}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.0258}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ResolveStencilBuffer", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.0876}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.1119}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PostSharpenPass", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0483}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0629}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "GTAO", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0194}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.014100000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BilateralUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.010400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 42, "outputVariants": 42, "name": "GenSdfRayMap", "pipelines": [{"inputVariants": 42, "outputVariants": 42, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0454}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0175}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0162}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0307}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldClearIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.006500000000000001}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugVectorscope", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.038400000000000004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "AmbientProbeConvolution", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InScatteredRadiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.009300000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "scrbound", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0085}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.019200000000000002}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "TemporalFilter", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0086}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0164}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0154}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0309}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearLightLists", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCircleOfConfusion", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.014}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0081}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0196}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFMinMaxDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0567}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ComputeGgxIblSampleData", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0103}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0227}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EyeCausticLUTGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "GPUPrefixSum", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0074}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.006900000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0471}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "VFXPrefixSum", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0118}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0074}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0061}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0077}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0451}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0077}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0056}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldKernel", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "SkyLUTGenerator", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.010700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.0431}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GTAOCopyHistory", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0089}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ClearDebugBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Texture3DAtlas", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0092}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0291}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "NaNKiller", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0176}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0119}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VFXFillIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.0066}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DepthOfFieldTileMax", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.019}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0456}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StageRasterFine", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0357}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "UpdateStrips", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0061}]}]}, {"inputVariants": 24, "outputVariants": 16, "name": "ScreenSpaceGlobalIllumination", "pipelines": [{"inputVariants": 24, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.0151}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.0099}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0362}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.023200000000000002}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.01}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0328}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Exposure", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0056}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0178}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "WaterDeformation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.015600000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0067}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "GTAOBlurAndUpsample", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0086}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0165}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "PlanarReflectionFiltering", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.042300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0162}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ProbeVolumeBlendStates", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0454}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GPUCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0381}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpPreTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.10360000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.0843}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "EncodeBC6H", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.0183}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "SubsurfaceScattering", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.038400000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.079}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.1153}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.07970000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "OcclusionCullingDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.017}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 384, "outputVariants": 256, "name": "VolumetricLighting", "pipelines": [{"inputVariants": 384, "outputVariants": 256, "pipeline": "", "variants": [{"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 1.1899}, {"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 1.2387000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DiffuseDenoiser", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.0239}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0267}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFComputeSlowTiles", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.016}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BuildProbabilityTables", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0097}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "FourierTransform", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0098}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0391}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.015700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumeVoxelization", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0112}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0085}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "WaterEvaluation", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.09000000000000001}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.09720000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "AlphaCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0108}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0048000000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFCoCMinMax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.0083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.0043}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldCombine", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.15180000000000002}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.1207}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "VrsTexture", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.1257}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.07050000000000001}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.084}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.0512}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "MotionBlurMergeTilePass", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.017400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0098}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ClearUIntTextureArray", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0122}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 160, "outputVariants": 160, "name": "ScreenSpaceReflections", "pipelines": [{"inputVariants": 160, "outputVariants": 160, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.0188}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0274}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.014700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.0458}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.0122}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.0122}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.0459}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.0125}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.0125}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0247}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.012400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0122}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.025}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.038}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0132}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.028900000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.013900000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0521}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.042}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0182}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.014700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.0516}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.012400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.0145}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.0129}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.0509}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.0129}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0281}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.0473}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0132}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.04}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.0149}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0403}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0145}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0407}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DepthOfFieldMip", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.020200000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCombine", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.0674}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.0155}]}]}, {"inputVariants": 10, "outputVariants": 0, "name": "StageWorkQueue", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0349}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFApertureShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.013600000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.0216}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "GPUSort", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0213}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0461}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0281}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "GenerateMaxZ", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.0211}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0114}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.0088}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.006900000000000001}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Sort", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.01}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0358}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0149}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.0175}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.0161}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GroundIrradiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0461}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Accumulation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0177}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.013900000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "MotionBlurMotionVecPrep", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0631}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0313}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumetricLightingFiltering", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0117}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "EdgeAdaptiveSpatialUpsampling", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0495}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.058300000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHDRxyMapping", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.0081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 256, "outputVariants": 128, "name": "UberPost", "pipelines": [{"inputVariants": 256, "outputVariants": 128, "pipeline": "", "variants": [{"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.9005000000000001}, {"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.8489}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldPrefilter", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.2617}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.264}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugLightVolumes", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0066}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0016}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "InstanceOcclusionCullingKernels", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.18280000000000002}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.13720000000000002}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.19060000000000002}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.0359}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.1047}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.1817}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferCopyKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.0227}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.0081}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearBuffer2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.0111}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EVSMBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0092}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.006500000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "materialflags", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.0848}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.0614}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "cleardispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceWindDataUpdateKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.0079}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.005200000000000001}]}]}, {"inputVariants": 20, "outputVariants": 0, "name": "StageShadingSetup", "pipelines": [{"inputVariants": 20, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.07490000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0018000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0685}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0027}]}]}]}