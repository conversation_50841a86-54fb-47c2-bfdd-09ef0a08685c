.AlertRootContainer {
    flex-grow: 1;
}

.AlertMainContainer {
    flex-grow: 1;
    justify-content: flex-end;
    flex-shrink: 0;
    align-self: flex-end;
    margin-right: 20px;
}

.NotificationContainer {
    background-color: rgb(16, 16, 16);
    width: 445px;
    flex-grow: 0;
    margin-top: 8px;
    padding: 8px 16px;
    border-width: 1px;
    border-color: rgba(33, 33, 33, 0);
}

.lable_text {
    color: rgb(255, 255, 255);
    margin: 0;
    padding: 0;
    -unity-text-align: middle-left;
    font-size: 24px;
}

.notification-enter {
    opacity: 0;
    translate: 0 100%;
    transition-property: opacity, translate;
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
}

.notification-exit {
    opacity: 0;
    transition-property: opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-in;
}

/* 
 * BATTERY NOTIFICATION STYLES
 */
.battery-notification-container {
    padding: 8px 24px;
    background-color: rgba(16, 16, 16, 0.61);
    height: 40px;
    justify-content: center;
    flex-direction: column;
}

/* Notification battery indicator */
.notification-battery-indicator {
    height: 20px;
    flex-direction: row;
    align-items: center;
    background-color: transparent;
}

/* Notification battery icon */
.notification-battery-icon {
    width: 24px;
    height: 24px;
    -unity-background-scale-mode: scale-to-fit;
    background-color: transparent;
}

/* Notification battery bar */
.notification-battery-bar {
    flex-grow: 1;
    height: 8px;
    margin-left: 16px;
    flex-direction: row;
    position: relative;
    align-items: center;
    justify-content: flex-start;
    background-color: transparent;
    border-width: 0;
    flex-wrap: nowrap;
}

/* Battery segments for notifications */
.battery-segment-container {
    flex-grow: 0;
    flex-shrink: 0;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-width: 0;
    border-color: transparent;
    background-color: rgb(20, 20, 20);
}

.battery-notification-container .battery-segment-container {
    width: 9%;
    flex-grow: 0;
    flex-shrink: 0;
    height: 10px;
    border-width: 1px;
    border-color: rgb(40, 40, 40);
    background-color: rgb(20, 20, 20);
    margin-right: 1%;
}

/* Remove margin from last notification segment */
.battery-notification-container .battery-segment-container:last-child {
    margin-right: 0;
}

.battery-notification-container .battery-segment {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0%;
    transition: width 0.1s ease-out;
    border-width: 0;
}

.battery-segment {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    transition: width 0.1s ease-out;
    border-width: 0;
}

.battery-notification-container .battery-segment-fill {
    background-color: rgb(120, 120, 120);
    border-radius: 1px;
}

.battery-segment-fill {
    background-color: rgb(120, 120, 120);
    border-radius: 1px;
}

/* Blinking animation for depleted segments */
.battery-segment-blink {
    animation: blink-animation 0.5s ease-in-out infinite;
}

@keyframes blink-animation {
    0% {
        opacity: 1;
        background-color: rgb(20, 20, 20);
    }
    50% {
        opacity: 0.3;
        background-color: rgba(200, 50, 50, 0.3);
    }
    100% {
        opacity: 1;
        background-color: rgb(20, 20, 20);
    }
}

/* Segment count specific styling */
.notification-battery-bar.segments-10 .battery-segment-container {
    width: 9%;
    margin-right: 1%;
}

.notification-battery-bar.segments-5 .battery-segment-container {
    width: 18%;
    margin-right: 2%;
}

.notification-battery-bar.segments-20 .battery-segment-container {
    width: 4.5%;
    margin-right: 0.5%;
}