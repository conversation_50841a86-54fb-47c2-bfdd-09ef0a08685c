package eu.ha3.presencefootsteps;

import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;
import com.google.gson.GsonBuilder;
import com.minelittlepony.common.util.settings.Config;
import com.minelittlepony.common.util.settings.HeirarchicalJsonConfigAdapter;
import com.minelittlepony.common.util.settings.Setting;
import com.minelittlepony.common.util.settings.ToStringAdapter;
import eu.ha3.presencefootsteps.config.EntitySelector;
import eu.ha3.presencefootsteps.config.VolumeOption;
import eu.ha3.presencefootsteps.sound.generator.Locomotion;
import net.minecraft.entity.EntityType;
import net.minecraft.registry.Registries;
import net.minecraft.util.Identifier;
import net.minecraft.util.crash.CrashReportSection;
import net.minecraft.util.math.MathHelper;

public class PFConfig extends Config {

    private final Setting<Boolean> disabled = value("client", "disabled", false)
            .addComment("Disables all functionality of the mod");

    private final Setting<Integer> volume = value("volume", "volume", 70)
            .addComment("Sets the global footsteps volume on a value between 0% and 100%")
            .addComment("Default: 70");
    public final Setting<Integer> runningVolumeIncrease = value("volume", "runningVolumeIncrease", 0)
            .addComment("Controls how much the footsteps sound changes when running on a percentage range between -100% to +100%")
            .addComment("Default: 0");

    public VolumeOption clientPlayerVolume = new VolumeOption(this, value("volume", "clientPlayerVolume", 100)
            .addComment("Sets the volume of your own footsteps on a value between 0% and 100%")
            .addComment("Default: 100"));
    public VolumeOption otherPlayerVolume = new VolumeOption(this, value("volume", "otherPlayerVolume", 100)
            .addComment("Sets the volume of other players' footsteps on a value between 0% and 100%")
            .addComment("Default: 100"));
    public VolumeOption hostileEntitiesVolume = new VolumeOption(this, value("volume", "hostileEntitiesVolume", 100)
            .addComment("Sets the volume of hostile mob's footsteps on a value between 0% and 100%")
            .addComment("Default: 100"));
    public VolumeOption passiveEntitiesVolume = new VolumeOption(this, value("volume", "passiveEntitiesVolume", 100)
            .addComment("Sets the volume of passive and friendly mob's footsteps on a value between 0% and 100%")
            .addComment("Default: 100"));

    public VolumeOption wetSoundsVolume = new VolumeOption(this, value("volume", "wetSoundsVolume", 50)
            .addComment("Sets the volume of sounds generated by walking on wet surfaces during rain or thunderstorms")
            .addComment("Default: 50"));
    public VolumeOption foliageSoundsVolume = new VolumeOption(this, value("volume", "foliageSoundsVolume", 100)
            .addComment("Sets the volume of sounds generated due to walking through foliage and brush")
            .addComment("Default: 50"));

    private final Setting<Integer> maxSteppingEntities = value("performance", "maxSteppingEntities", 50)
            .addComment("Controls the maximum number of entities the mod can generate footsteps for in a single frame")
            .addComment("Default: 50");

    private final Setting<Boolean> multiplayer = value("sound", "multiplayer", true)
            .addComment("Sets whether footsteps should be generated for other players when in multiplayer");
    private final Setting<Boolean> global = value("sound", "global", true)
            .addComment("Sets whether footsteps should be generated for other (non-player) mobs");
    private final Setting<Boolean> footwear = value("sound", "footwear", true)
            .addComment("When enabled, special footstep sounds will be generated matching the boots you are wearing.")
            .addComment("Note not all types of boots are supported. Modded boots may sound strange or not work.");
    private final Setting<Boolean> visualiser = value("debug", "visualiser", false)
            .addComment("Enables a visualiser to show where sound/material checks are occuring");
    private final Setting<Boolean> exclusive = value("sound", "exclusive", false)
            .addComment("Sets whether to block vanilla footstep sounds when generating sounds for other entities or players");

    private final Setting<Locomotion> stance = value("client", "stance", Locomotion.NONE)
            .addComment("Sets the player's own stance (type of footsteps to generate")
            .addComment("Options:")
            .addComment("  NONE (determined by whether Mine Little Pony is installed)")
            .addComment("  BIPED (always two-legged)")
            .addComment("  QUADRUPED (always four-legged)")
            .addComment("  FLYING (always four-legged + wings)")
            .addComment("  FLYING_BIPED (always two-legged + wings)")
            .addComment("Default: NONE");

    private final Setting<EntitySelector> targetEntities = value("client", "targetEntities", EntitySelector.ALL)
            .addComment("Controls which group of entities to generate footsteps for")
            .addComment("Options: ALL, PLAYERS_AND_HOSTILES, PLAYERS_ONLY")
            .addComment("Default: ALL");

    public final Setting<Set<Identifier>> ignoredEntityTypes = this.<Identifier, Set<Identifier>>value("compatibility", "ignoredEntityTypes", () -> new HashSet<Identifier>(Set.of(
                Identifier.ofVanilla("ghast"),
                Identifier.ofVanilla("happy_ghast"),
                Identifier.ofVanilla("phantom")
            )), Identifier.class)
            .addComment("A list of entity type for any types of mobs that should never produce footsteps. Usually should be any flying 'bird' mobs.")
            .addComment("Default: minecraft:ghast, minecraft:happy_ghast, minecraft:phantom");


    private transient final PresenceFootsteps pf;

    public PFConfig(Path file, PresenceFootsteps pf) {
        super(new HeirarchicalJsonConfigAdapter(new GsonBuilder()
                .registerTypeAdapter(Identifier.class, new ToStringAdapter<>(Identifier::toString, Identifier::of))
        ), file);
        this.pf = pf;
    }

    public boolean toggleMultiplayer() {
        multiplayer.set(!multiplayer.get());
        save();

        return multiplayer.get();
    }

    public boolean isIgnoredForFootsteps(EntityType<?> type) {
        return this.ignoredEntityTypes.get().contains(Registries.ENTITY_TYPE.getId(type));
    }

    public EntitySelector cycleTargetSelector() {
        targetEntities.set(EntitySelector.VALUES[(getEntitySelector().ordinal() + 1) % EntitySelector.VALUES.length]);

        save();

        return targetEntities.get();
    }

    public Locomotion setLocomotion(Locomotion loco) {
        if (loco != getLocomotion()) {
            stance.set(loco);
            save();

            pf.getEngine().reload();
        }

        return loco;
    }

    public boolean isVisualiserRunning() {
        return visualiser.get();
    }

    public Locomotion getLocomotion() {
        return stance.get() == null ? Locomotion.NONE : stance.get();
    }

    public EntitySelector getEntitySelector() {
        return targetEntities.get() == null ? EntitySelector.ALL : targetEntities.get();
    }

    public boolean getEnabledFootwear() {
        return footwear.get();
    }

    public boolean toggleFootwear() {
        footwear.set(!footwear.get());
        save();
        return footwear.get();
    }

    public boolean isExclusiveMode() {
        return exclusive.get();
    }

    public boolean toggleExclusiveMode() {
        exclusive.set(!exclusive.get());
        save();
        return exclusive.get();
    }

    public boolean getEnabledMP() {
        return multiplayer.get();
    }

    public int getMaxSteppingEntities() {
        return Math.max(1, maxSteppingEntities.get());
    }

    public boolean toggleDisabled() {
        disabled.set(!disabled.get());
        save();
        pf.onEnabledStateChange(!disabled.get());
        return disabled.get();
    }

    public boolean setDisabled(boolean disabled) {
        if (disabled != this.disabled.get()) {
            toggleDisabled();
        }
        return disabled;
    }

    public boolean getDisabled() {
        return disabled.get();
    }

    public boolean getEnabled() {
        return !disabled.get() && getGlobalVolume() > 0;
    }

    public int getGlobalVolume() {
        return MathHelper.clamp(volume.get(), 0, 100);
    }

    public int getRunningVolumeIncrease() {
        return MathHelper.clamp(runningVolumeIncrease.get(), -100, 100);
    }

    public float setGlobalVolume(float volume) {
        int vvolume = volumeScaleToInt(volume);

        if (this.volume.get() != vvolume) {
            boolean wasEnabled = getEnabled();

            this.volume.set(vvolume);
            save();

            if (getEnabled() != wasEnabled) {
                pf.onEnabledStateChange(getEnabled());
            }
        }

        return getGlobalVolume();
    }

    public float setRunningVolumeIncrease(float volume) {
        runningVolumeIncrease.set(volume > 97 ? 100 : volume < -97 ? -100 : (int)volume);
        save();
        return getRunningVolumeIncrease();
    }

    public void populateCrashReport(CrashReportSection section) {
        section.add("Disabled", getDisabled());
        section.add("Global Volume", volume.get());
        section.add("User's Selected Stance", getLocomotion());
        section.add("Target Selector", getEntitySelector());
        section.add("Enabled Global", global.get());
        section.add("Enabled Multiplayer", multiplayer.get());
    }

    private static int volumeScaleToInt(float volume) {
        return volume > 97 ? 100 : volume < 3 ? 0 : (int)volume;
    }
}
