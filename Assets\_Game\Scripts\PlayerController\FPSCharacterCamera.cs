using UnityEngine;
using System.Collections;

namespace KinematicCharacterController.FPS
{
    public class FPSCharacterCamera : MonoBehaviour
    {
        [Header("References")]
        public Camera Camera;
        public Transform CameraFollowTarget;
        public FPSCharacterController CharacterController;

        [Header("Look Settings")]
        public bool InvertX = false;
        public bool InvertY = false;
        public float LookSpeed = 2f;

        [Header("View Limits")]
        [Range(-90f, 90f)]
        public float MaxUpAngle = 90f;
        [Range(-90f, 90f)]
        public float MaxDownAngle = -90f;

        [Header("Zoom Settings")]
        public KeyCode ZoomKey = KeyCode.X;
        public float ZoomedFOV = 30f;
        public float ZoomSpeed = 8f;
        public float ZoomSensitivityModifier = 0.5f;

        // Don't override this with Inspector values - get from settings
        [HideInInspector]
        public float FieldOfView = 90f;

        private float _targetVerticalAngle;
        private float _targetHorizontalAngle;
        private bool _hasInitializedRotation;
        private bool isPaused;
        private bool hasAppliedInitialSettings = false;

        // Zoom variables
        private bool isZooming = false;
        private float normalFOV;
        private float targetFOV;
        private float zoomSensitivityModifier = 1.0f;
        private CrosshairManager crosshairManager;

        // Grab rotation mode flag
        private bool _grabRotationMode = false;

        // Flag to communicate with other scripts
        public bool IsCurrentlyZooming => isZooming;

        private bool _inventoryMode = false;

        // Tool-based zoom control
        private bool _canZoom = false;
        
        // Vehicle mode control
        private bool _vehicleMode = false;

        private void Awake()
        {
            // Initialize camera reference early
            if (Camera == null)
                Camera = GetComponent<Camera>();

            // Try to find crosshair manager
            crosshairManager = FindObjectOfType<CrosshairManager>();
        }

        private void Start()
        {
            // Initialize camera reference if not set
            if (Camera == null)
                Camera = GetComponent<Camera>();

            if (!_hasInitializedRotation)
            {
                _targetHorizontalAngle = transform.eulerAngles.y;
                _targetVerticalAngle = transform.eulerAngles.x;
                if (_targetVerticalAngle > 180f)
                    _targetVerticalAngle -= 360f;
                _hasInitializedRotation = true;
            }

            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;

            // Subscribe to settings manager events
            if (SettingsVideoManager.Instance != null)
            {
                Debug.Log("FPSCamera: Subscribing to FOV changes");
                SettingsVideoManager.Instance.OnFOVChanged += HandleFOVChanged;

                // Apply initial settings
                ApplyInitialSettings();
            }
            else
            {
                Debug.LogWarning("SettingsManager not found when initializing FPSCharacterCamera");
                // Fallback to using camera's current FOV if settings manager is not available
                if (Camera != null)
                {
                    FieldOfView = Camera.fieldOfView;
                    normalFOV = FieldOfView;
                    targetFOV = normalFOV;
                }
            }
        }

        private void ApplyInitialSettings()
        {
            // This ensures we only apply settings once during startup
            if (!hasAppliedInitialSettings && SettingsVideoManager.Instance != null)
            {
                FieldOfView = SettingsVideoManager.Instance.FieldOfView;
                if (Camera != null)
                {
                    Camera.fieldOfView = FieldOfView;
                    Debug.Log($"Applied initial FOV: {FieldOfView}");

                    // Update zoom normal FOV when settings are applied
                    normalFOV = FieldOfView;
                    targetFOV = normalFOV;
                }
                hasAppliedInitialSettings = true;
            }
        }

        private void OnEnable()
        {
            // Reapply settings when the camera is enabled
            ApplyInitialSettings();

            // Ensure zoom state is reset when enabled
            isZooming = false;
            _grabRotationMode = false;
            if (Camera != null)
            {
                normalFOV = Camera.fieldOfView;
                targetFOV = normalFOV;
            }
            zoomSensitivityModifier = 1.0f;
        }


        private void OnDestroy()
        {
            // Unsubscribe from settings manager events
            if (SettingsVideoManager.Instance != null)
            {
                SettingsVideoManager.Instance.OnFOVChanged -= HandleFOVChanged;
            }
        }

        private void HandleFOVChanged(float newFOV)
        {
            FieldOfView = newFOV;
            normalFOV = newFOV;

            // Only update camera FOV if not zooming
            if (!isZooming && Camera != null)
            {
                targetFOV = newFOV;
                Debug.Log($"Updated camera FOV to: {newFOV}");
            }
        }

        public void SetPauseState(bool paused)
        {
            isPaused = paused;

            // If unpausing (e.g., closing inventory), ensure cursor is locked
            if (!paused)
            {
                // Small delay to ensure this happens after inventory processing
                StartCoroutine(DelayedCursorLock());
            }
            // If paused, make sure we reset zoom
            else if (paused && isZooming)
            {
                StopZoom();
            }
        }

        private IEnumerator DelayedCursorLock()
        {
            yield return new WaitForEndOfFrame();
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }

        private void Update()
        {
            // Handle zoom input - don't zoom in inventory or grab rotation mode
            // Also only zoom if canZoom is true (hand tool selected)
            if (!isPaused && !_inventoryMode && !_grabRotationMode && _canZoom)
            {
                HandleZoom();
            }
        }

        private void LateUpdate()
        {
            if (isPaused)
            {
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                return;
            }

            if (CameraFollowTarget == null || CharacterController == null)
                return;

            // Always update position to follow the character
            HandlePosition();

            // Only handle rotation if not in inventory mode and not in grab rotation mode
            if (!_inventoryMode && !_grabRotationMode)
            {
                HandleRotation();
            }

            // Apply FOV changes in LateUpdate to ensure we're the last to modify it
            if (Camera != null && isZooming)
            {
                // Smoothly adjust FOV
                Camera.fieldOfView = Mathf.Lerp(Camera.fieldOfView, targetFOV, Time.deltaTime * ZoomSpeed);
            }
        }

        private void HandlePosition()
        {
            // Direct position setting with no smoothing
            transform.position = CameraFollowTarget.position;
        }

        private void HandleRotation()
        {
            // Return early if paused, in grab rotation mode, inventory mode, or vehicle mode
            if (isPaused || _grabRotationMode || _inventoryMode || _vehicleMode)
            {
                return;
            }

            // Only process rotation if cursor is locked
            if (Cursor.lockState == CursorLockMode.Locked)
            {
                float mouseX = Input.GetAxis("Mouse X") * (InvertX ? -1 : 1);
                float mouseY = Input.GetAxis("Mouse Y") * (InvertY ? -1 : 1);

                // Apply zoom sensitivity modifier
                mouseX *= LookSpeed * zoomSensitivityModifier;
                mouseY *= LookSpeed * zoomSensitivityModifier;

                _targetHorizontalAngle += mouseX;
                _targetVerticalAngle -= mouseY;

                // Clamp vertical rotation
                _targetVerticalAngle = Mathf.Clamp(_targetVerticalAngle, MaxDownAngle, MaxUpAngle);

                // Apply rotation
                transform.rotation = Quaternion.Euler(_targetVerticalAngle, _targetHorizontalAngle, 0f);

                // Update character rotation instantly
                Vector3 characterTargetRotation = CharacterController.transform.eulerAngles;
                characterTargetRotation.y = _targetHorizontalAngle;
                CharacterController.transform.rotation = Quaternion.Euler(characterTargetRotation);
            }
        }

        private void HandleZoom()
        {
            // Don't handle zoom in inventory mode or grab rotation mode or if zoom is not allowed
            if (isPaused || _inventoryMode || _grabRotationMode || !_canZoom)
                return;

            // Check for zoom input
            bool zoomKeyPressed = Input.GetKey(ZoomKey);

            // Toggle zoom state if needed
            if (zoomKeyPressed && !isZooming)
            {
                StartZoom();
            }
            else if (!zoomKeyPressed && isZooming)
            {
                StopZoom();
            }
        }

        private void StartZoom()
        {
            // Don't start zoom if in inventory or grab rotation mode or if zoom is not allowed
            if (_inventoryMode || _grabRotationMode || !_canZoom)
                return;

            isZooming = true;
            zoomSensitivityModifier = ZoomSensitivityModifier;
            targetFOV = ZoomedFOV;

            // Explicitly ensure cursor remains hidden during zoom
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;

            // REMOVED: Don't modify crosshair during zoom
            // The CrosshairManager handles its own visibility based on proximity
        }

        private void StopZoom()
        {
            isZooming = false;
            zoomSensitivityModifier = 1.0f;
            targetFOV = normalFOV;

            // Ensure cursor remains hidden after zoom
            if (!isPaused && !_inventoryMode)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;
            }

            // REMOVED: Don't modify crosshair when zoom ends
            // The CrosshairManager handles its own visibility based on proximity
        }

        public void SetCursorLock(bool lockCursor)
        {
            Cursor.lockState = lockCursor ? CursorLockMode.Locked : CursorLockMode.None;
            Cursor.visible = !lockCursor;
        }

        public void SetInitialRotation(float verticalAngle, float horizontalAngle)
        {
            _targetVerticalAngle = verticalAngle;
            _targetHorizontalAngle = horizontalAngle;
            _hasInitializedRotation = true;

            // Force update the rotation immediately
            transform.rotation = Quaternion.Euler(_targetVerticalAngle, _targetHorizontalAngle, 0f);
        }

        public void SetZoomModifier(float modifier)
        {
            ZoomSensitivityModifier = Mathf.Clamp(modifier, 0.1f, 1.0f);
        }

        /// <summary>
        /// Sets the grab rotation mode which disables camera rotation while rotating grabbed objects
        /// </summary>
        public void SetGrabRotationMode(bool enabled)
        {
            _grabRotationMode = enabled;

            // If enabling grab rotation mode, ensure cursor is locked
            // and exit zoom if active
            if (enabled)
            {
                Cursor.lockState = CursorLockMode.Locked;
                Cursor.visible = false;

                if (isZooming)
                {
                    StopZoom();
                }
            }
        }

        public void SetInventoryMode(bool enabled)
        {
            _inventoryMode = enabled;

            // Configure the cursor
            Cursor.lockState = enabled ? CursorLockMode.None : CursorLockMode.Locked;
            Cursor.visible = enabled;

            // If enabling inventory mode, make sure we exit zoom
            if (enabled && isZooming)
            {
                StopZoom();
            }
        }

        /// <summary>
        /// Sets whether zooming is allowed based on the currently selected tool
        /// </summary>
        public void SetCanZoom(bool canZoom)
        {
            _canZoom = canZoom;

            // If we're currently zooming but no longer allowed to zoom, stop zooming
            if (!canZoom && isZooming)
            {
                StopZoom();
            }
        }
        
        /// <summary>
        /// Sets vehicle mode which disables all camera input processing to prevent conflicts
        /// </summary>
        public void SetVehicleMode(bool vehicleMode)
        {
            _vehicleMode = vehicleMode;
            
            // If entering vehicle mode, stop zoom and ensure proper state
            if (vehicleMode)
            {
                if (isZooming)
                {
                    StopZoom();
                }
                // Don't change cursor state - let vehicle system handle it
            }
        }

        // Method to allow external systems to modify camera's vertical angle
        public void SetCameraRotationX(float angle)
        {
            // Clamp to view limits
            _targetVerticalAngle = Mathf.Clamp(angle, MaxDownAngle, MaxUpAngle);

            // Update camera rotation
            transform.rotation = Quaternion.Euler(_targetVerticalAngle, _targetHorizontalAngle, 0f);
        }
    }
}