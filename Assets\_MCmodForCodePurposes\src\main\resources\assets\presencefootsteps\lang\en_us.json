{"mod.presencefootsteps.name": "Presence Footsteps", "key.category.presencefootsteps": "Presence Footsteps", "key.presencefootsteps.settings": "Open Settings", "key.presencefootsteps.toggle": "Toggle Mod", "key.presencefootsteps.toggle.enabled": "Sounds Enabled", "key.presencefootsteps.toggle.disabled": "Sounds Disabled", "pf.default_sounds.name": "Default Sound Pack", "pf.default_sounds.missing": "No sound packs detected. Go to resource packs and enable the %s to get started.", "menu.pf.title": "Presence Footsteps Options", "menu.pf.multiplayer.true": "Single + Multiplayer", "menu.pf.multiplayer.false": "Singleplayer Only", "menu.pf.global.all": "Entities: All", "menu.pf.global.players_and_hostiles": "Entities: Players + Hostiles", "menu.pf.global.players_only": "Entities: Only Players", "menu.pf.volume.min": "Global Volume: OFF", "menu.pf.disable_mod": "Disable All Sounds", "menu.pf.volume": "Global Volume: %d%%", "menu.pf.volume.tooltip": "The global volume of all presence footsteps sounds. Set to 0 if you wish to disable the mod entirely.", "menu.pf.volume.hostile_entities": "Hostile Entities: %d%%", "menu.pf.volume.hostile_entities.tooltip": "Changes the volume of enemies' footsteps.", "menu.pf.volume.passive_entities": "Passive Entities: %d%%", "menu.pf.footwear.on": "Footwear: ON", "menu.pf.footwear.off": "Footwear: OFF", "menu.pf.exclusive_mode.on": "Exclusive Mode: ON", "menu.pf.exclusive_mode.off": "Exclusive Mode: OFF", "menu.pf.volume.passive_entities.tooltip": "Changes the volume of animal' footsteps.", "menu.pf.volume.player": "Client Player: %d%%", "menu.pf.volume.player.tooltip": "Changes the volume of your own footsteps.", "menu.pf.volume.other_players": "Other Players: %d%%", "menu.pf.volume.other_players.tooltip": "Changes the volume of other players' footsteps when in multiplayer.", "menu.pf.volume.running": "Speed Attenuation: %d%%", "menu.pf.volume.running.tooltip": "How much the footsteps' volume can change when running.\n\nThis value is ADDED to the total volume will scale with your speed.\n\nIt's recommended to set this to something small first then make adjustments as needed. Larger values will make the effect more pronounced.", "menu.pf.volume.wet": "Wet surfaces: %s%%", "menu.pf.volume.wet.tooltip": "Changes the volume of sounds created when walking on blocks that are wet.", "menu.pf.volume.foliage": "Foliage: %s%%", "menu.pf.volume.foliage.tooltip": "Changes the volume of sounds created when walking through plants and tall grass.", "menu.pf.stance": "Stance: %s", "menu.pf.stance.auto": "Auto", "menu.pf.stance.none": "None", "menu.pf.stance.none.tooltip": "Auto will pick an appropriate stance based on Mine Little Pony. If that mod is not installed, behaves identical to Biped", "menu.pf.stance.quadruped": "Quadrupedal", "menu.pf.stance.quadruped.tooltip": "Quadrupedal emulates a 4-legged walking mode", "menu.pf.stance.biped": "Bipedal", "menu.pf.stance.biped.tooltip": "Bipedal emulates a 2-legged (human) walking mode", "menu.pf.stance.flying": "Pegasus", "menu.pf.stance.flying.tooltip": "Pegasus emulates a 4-legged walking mode with wings", "menu.pf.stance.flying_biped": "Avian", "menu.pf.stance.flying_biped.tooltip": "Avian emulates a 2-legged walking mode with wings", "menu.pf.report.full": "Full report", "menu.pf.report.concise": "Concise report", "menu.pf.report.acoustics": "Dump Acoustics", "menu.pf.group.volume": "Volume", "menu.pf.group.footsteps": "Footsteps", "menu.pf.group.debugging": "Debugging", "menu.pf.group.sound_packs": "Sound Packs", "pf.report.save": "File saved as: %s", "pf.report.error": "Failed to generate report: %s", "pf.update.title": "An Update is Available", "pf.update.text": "Presence Footsteps %s", "pf.update.checking": "Checking for updates...", "pf.update.updates_available": "An update is available!\n Presence Footsteps %s for MC%s", "pf.update.up_to_date": "You are up to date", "subtitles.pf.footsteps": "Footsteps", "subtitles.pf.wood_squeak": "Wood squeaks", "subtitles.pf.wood_bends": "Wood bends", "subtitles.pf.wood_creaks": "Wood creaks", "subtitles.pf.swim": "Water flows", "subtitles.pf.wings_flap": "Wings flap", "subtitles.pf.leaves_rustle": "Leaves rustles", "subtitles.pf.grass_rustles": "Grass rustles", "subtitles.pf.fire_flames": "Fire Hisses", "subtitles.pf.land": "Wings folding", "subtitles.pf.metal_clink": "Metal clinking", "subtitles.pf.snow_crunch": "Snow crunches", "subtitles.pf.sand_crunch": "Sand crunches", "subtitles.pf.grass_crunch": "Grass crunches", "subtitles.pf.gravel.crunch": "Gravel crunches", "subtitles.pf.ice_crack": "Ice cracks", "subtitles.pf.mud_squealk": "Mud squishes", "subtitles.pf.glass_rattles": "Glass rattles", "subtitles.pf.glass_croak": "<PERSON> groans", "subtitles.pf.chain": "Chain Jingles", "subtitles.pf.stone_crack": "Stone crumbles", "subtitles.pf.wax_squeak": "Wax squeaks"}