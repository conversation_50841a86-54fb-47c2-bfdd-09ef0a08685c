{ "pid": 234912, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 234912, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 234912, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 234912, "tid": 1, "ts": 1754343144048619, "dur": 423337, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 234912, "tid": 1, "ts": 1754343144050308, "dur": 56289, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144056878, "dur": 47240, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144135553, "dur": 15877, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144152847, "dur": 89298, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144242182, "dur": 19104, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144261303, "dur": 200972, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144468730, "dur": 3019, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144471957, "dur": 487, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144481709, "dur": 3214, "ph": "X", "name": "", "args": {} },
{ "pid": 234912, "tid": 1, "ts": 1754343144480716, "dur": 4616, "ph": "X", "name": "Write chrome-trace events", "args": {} },
