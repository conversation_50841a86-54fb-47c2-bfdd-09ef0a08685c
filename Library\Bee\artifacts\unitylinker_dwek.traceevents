{ "pid": 240240, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 240240, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 240240, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 240240, "tid": 1, "ts": 1754346507951370, "dur": 424767, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 240240, "tid": 1, "ts": 1754346507953466, "dur": 63711, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346507961794, "dur": 53694, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508044110, "dur": 15944, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508061349, "dur": 83850, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508145292, "dur": 21936, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508167248, "dur": 199786, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508373483, "dur": 2467, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508376139, "dur": 445, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508385366, "dur": 3036, "ph": "X", "name": "", "args": {} },
{ "pid": 240240, "tid": 1, "ts": 1754346508384548, "dur": 4347, "ph": "X", "name": "Write chrome-trace events", "args": {} },
