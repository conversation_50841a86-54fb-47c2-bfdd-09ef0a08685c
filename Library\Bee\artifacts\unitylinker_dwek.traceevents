{ "pid": 169540, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 169540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 169540, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 169540, "tid": 1, "ts": 1754345729170446, "dur": 422024, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 169540, "tid": 1, "ts": 1754345729172426, "dur": 51944, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729178307, "dur": 44496, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729251224, "dur": 16178, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729268726, "dur": 78065, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729346848, "dur": 19411, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729366277, "dur": 215853, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729589525, "dur": 2737, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729592472, "dur": 584, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729601799, "dur": 3475, "ph": "X", "name": "", "args": {} },
{ "pid": 169540, "tid": 1, "ts": 1754345729600988, "dur": 4926, "ph": "X", "name": "Write chrome-trace events", "args": {} },
