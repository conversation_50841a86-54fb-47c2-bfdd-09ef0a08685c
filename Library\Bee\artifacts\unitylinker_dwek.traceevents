{ "pid": 98468, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 98468, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 98468, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 98468, "tid": 1, "ts": 1754341031687624, "dur": 395716, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 98468, "tid": 1, "ts": 1754341031689815, "dur": 60212, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341031698192, "dur": 49748, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341031775381, "dur": 13827, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341031790398, "dur": 76459, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341031866906, "dur": 17928, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341031884848, "dur": 189368, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341032080857, "dur": 2316, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341032083341, "dur": 408, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341032093458, "dur": 3399, "ph": "X", "name": "", "args": {} },
{ "pid": 98468, "tid": 1, "ts": 1754341032092561, "dur": 4762, "ph": "X", "name": "Write chrome-trace events", "args": {} },
