{ "pid": 189192, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 189192, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 189192, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 189192, "tid": 1, "ts": 1754338809749045, "dur": 2066537, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 189192, "tid": 1, "ts": 1754338809752678, "dur": 437917, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338810103076, "dur": 85222, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338810262937, "dur": 16131, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338810280381, "dur": 169381, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338810449823, "dur": 1125314, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338811575171, "dur": 224954, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338811812342, "dur": 3011, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338811815585, "dur": 544, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338811826592, "dur": 3616, "ph": "X", "name": "", "args": {} },
{ "pid": 189192, "tid": 1, "ts": 1754338811825333, "dur": 5663, "ph": "X", "name": "Write chrome-trace events", "args": {} },
