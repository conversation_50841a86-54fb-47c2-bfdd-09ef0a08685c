-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_1_1
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:MIRROR
-define:MIRROR_81_OR_NEWER
-define:MIRROR_82_OR_NEWER
-define:MIRROR_83_OR_NEWER
-define:MIRROR_84_OR_NEWER
-define:MIRROR_85_OR_NEWER
-define:MIRROR_86_OR_NEWER
-define:MIRROR_89_OR_NEWER
-define:MIRROR_90_OR_NEWER
-define:MIRROR_93_OR_NEWER
-define:EDGEGAP_PLUGIN_SERVERS
-define:RealtimeCSG
-define:RealtimeCSG_1
-define:RealtimeCSG_1_6
-define:RealtimeCSG_1_6_01
-define:BAKERY_INCLUDED
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Unity/Editors/6000.1.1f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting@7dcdc439b230/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/BakeryEditorAssembly.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/BakeryRuntimeAssembly.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Domain_Reload.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Tayx.Graphy.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Tayx.Graphy.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProGrids.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProGrids.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProjectAuditor.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProjectAuditor.Editor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Samples.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Samples.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TestTools.CodeCoverage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
-analyzer:"Library/PackageCache/com.unity.project-auditor@94c6e4e98816/RoslynAnalyzers/Domain_Reload_Analyzer.dll"
"Assets/_Game/Scripts/AudioSystems/AudioEventChannel.cs"
"Assets/_Game/Scripts/AudioSystems/AudioEventDefinition.cs"
"Assets/_Game/Scripts/AudioSystems/EnvironmentalAudioHandler.cs"
"Assets/_Game/Scripts/AudioSystems/GlobalAudioManager.cs"
"Assets/_Game/Scripts/AudioSystems/PlayerAudioHandler.cs"
"Assets/_Game/Scripts/AudioSystems/SettingsAudioManager.cs"
"Assets/_Game/Scripts/AudioSystems/UIAudioHandler.cs"
"Assets/_Game/Scripts/Core/VoidRescueCapsuleController.cs"
"Assets/_Game/Scripts/Core/VoidRescueSystem.cs"
"Assets/_Game/Scripts/DeathSys/AutoSafetySystem.cs"
"Assets/_Game/Scripts/DeathSys/DeathManager.cs"
"Assets/_Game/Scripts/DeathSys/FallDamageSystem.cs"
"Assets/_Game/Scripts/DeathSys/ItemBreakageSystem.cs"
"Assets/_Game/Scripts/DeathSys/WakeUpPoint.cs"
"Assets/_Game/Scripts/Experimental/AtmosphericFog.cs"
"Assets/_Game/Scripts/Experimental/AutoLight.cs"
"Assets/_Game/Scripts/Experimental/BrutalistPlaygroundGenerator.cs"
"Assets/_Game/Scripts/Experimental/cube-placement-tool.cs"
"Assets/_Game/Scripts/Experimental/dynamic_fog_controller.cs"
"Assets/_Game/Scripts/Experimental/Flair.cs"
"Assets/_Game/Scripts/Experimental/FlairGun.cs"
"Assets/_Game/Scripts/Experimental/FlairPool.cs"
"Assets/_Game/Scripts/Experimental/InteractiveWorldScreenEditor.cs"
"Assets/_Game/Scripts/Experimental/ItemCreationTool.cs"
"Assets/_Game/Scripts/Experimental/MirrorOrbitCamera.cs"
"Assets/_Game/Scripts/Experimental/Pip3.cs"
"Assets/_Game/Scripts/Experimental/PipeGenerator.cs"
"Assets/_Game/Scripts/Experimental/Screen.cs"
"Assets/_Game/Scripts/Experimental/WireCreator.cs"
"Assets/_Game/Scripts/FPSControllerSettingsUI.cs"
"Assets/_Game/Scripts/General/CurrencyManager.cs"
"Assets/_Game/Scripts/General/CurrencyManagerBootstrap.cs"
"Assets/_Game/Scripts/General/DevSceneInitializer.cs"
"Assets/_Game/Scripts/General/GameFlowManager.cs"
"Assets/_Game/Scripts/General/PlatformButtonController.cs"
"Assets/_Game/Scripts/General/PlayerProgressionManager.cs"
"Assets/_Game/Scripts/General/WorldItemManager.cs"
"Assets/_Game/Scripts/Interact/BaseInteractable.cs"
"Assets/_Game/Scripts/Interact/GrabInteraction.cs"
"Assets/_Game/Scripts/Interact/IInteractable.cs"
"Assets/_Game/Scripts/Interact/InteractableObject.cs"
"Assets/_Game/Scripts/Interact/InteractionManager.cs"
"Assets/_Game/Scripts/Interact/InteractionUI.cs"
"Assets/_Game/Scripts/Interface/BatteryController.cs"
"Assets/_Game/Scripts/Interface/Crosshair/CrosshairManager.cs"
"Assets/_Game/Scripts/Interface/Cursor/CustomCursor.cs"
"Assets/_Game/Scripts/Interface/Menu/BaseMenuManager.cs"
"Assets/_Game/Scripts/Interface/Menu/MenuCameraController.cs"
"Assets/_Game/Scripts/Interface/Menu/PauseMenu.cs"
"Assets/_Game/Scripts/Interface/Menu/SettingsManager.cs"
"Assets/_Game/Scripts/Interface/Menu/SettingsVideoManager.cs"
"Assets/_Game/Scripts/Interface/Menu/StartMenuManager.cs"
"Assets/_Game/Scripts/Interface/MoneyCounterAnimation.cs"
"Assets/_Game/Scripts/Interface/MoneyCounterInitializer.cs"
"Assets/_Game/Scripts/Interface/NotificationManager.cs"
"Assets/_Game/Scripts/Inv/ContainerGrid.cs"
"Assets/_Game/Scripts/Inv/GridRenderer.cs"
"Assets/_Game/Scripts/Inv/InvDragAndDropManager.cs"
"Assets/_Game/Scripts/Inv/InvDraggedItemVisual.cs"
"Assets/_Game/Scripts/Inv/InvDroppedStorageEquipment.cs"
"Assets/_Game/Scripts/Inv/InvEquipmentManager.cs"
"Assets/_Game/Scripts/Inv/InvItemContainer.cs"
"Assets/_Game/Scripts/Inv/InvItemDropping.cs"
"Assets/_Game/Scripts/Inv/InvItemModelSwapper.cs"
"Assets/_Game/Scripts/Inv/InvItemMover.cs"
"Assets/_Game/Scripts/Inv/InvItemPickup.cs"
"Assets/_Game/Scripts/Inv/InvItemSplitter.cs"
"Assets/_Game/Scripts/Inv/InvItemUtils.cs"
"Assets/_Game/Scripts/Inv/InvShiftClickHandler.cs"
"Assets/_Game/Scripts/Inv/InvSlotHandler.cs"
"Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs"
"Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs"
"Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs"
"Assets/_Game/Scripts/Inv/InvUI.cs"
"Assets/_Game/Scripts/Inv/ItemDropPrefab.cs"
"Assets/_Game/Scripts/Inv/ItemUniqueId.cs"
"Assets/_Game/Scripts/Inv/SerializableInventoryTypes.cs"
"Assets/_Game/Scripts/Items/Armor.cs"
"Assets/_Game/Scripts/Items/Bag.cs"
"Assets/_Game/Scripts/Items/ConsumableDefinition.cs"
"Assets/_Game/Scripts/Items/Helmet.cs"
"Assets/_Game/Scripts/Items/Item.cs"
"Assets/_Game/Scripts/Items/ItemRegistry.cs"
"Assets/_Game/Scripts/Items/Loot.cs"
"Assets/_Game/Scripts/Items/Manual.cs"
"Assets/_Game/Scripts/Items/ToolDefinition.cs"
"Assets/_Game/Scripts/Player/DebugFlyController.cs"
"Assets/_Game/Scripts/Player/PlayerDebugDisplay.cs"
"Assets/_Game/Scripts/Player/PlayerStatus.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/ICharacterController.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/IMoverController.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/KCCSettings.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/KinematicCharacterMotor.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/KinematicCharacterSystem.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/PhysicsMover.cs"
"Assets/_Game/Scripts/PlayerController/CoreController/ReadOnlyAttribute.cs"
"Assets/_Game/Scripts/PlayerController/FPSCharacterCamera.cs"
"Assets/_Game/Scripts/PlayerController/FPSCharacterController.cs"
"Assets/_Game/Scripts/PlayerController/FPSPlayerManager.cs"
"Assets/_Game/Scripts/PlayerController/HeadBob.cs"
"Assets/_Game/Scripts/PlayerController/HeadBobDebug.cs"
"Assets/_Game/Scripts/PlayerController/RagdollTumbleSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/ClimbingRopeSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/GrapplingHookSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/GrapplingHook/PredictionVisualizer.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/KinematicClimbingSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/KinematicVaultSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/KinematicWallRun.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/SlidingSystem.cs"
"Assets/_Game/Scripts/PlayerController/ShmovementSystem/SurfingSystem.cs"
"Assets/_Game/Scripts/PlayerController/ViewModelHeadBob.cs"
"Assets/_Game/Scripts/Ship/SpaceshipCameraController.cs"
"Assets/_Game/Scripts/Ship/SpaceshipVehicle.cs"
"Assets/_Game/Scripts/Ship/VehicleInteractable.cs"
"Assets/_Game/Scripts/Stash and Trade/ShopSystem.cs"
"Assets/_Game/Scripts/Stash and Trade/ShopUI.cs"
"Assets/_Game/Scripts/Stash and Trade/StashSystem.cs"
"Assets/_Game/Scripts/Stash and Trade/StashUI.cs"
"Assets/_Game/Scripts/Stash and Trade/TraderInventory.cs"
"Assets/_Game/Scripts/Stash and Trade/UIPanelManager.cs"
"Assets/_Game/Scripts/Tool/ClimbingSystemActivator.cs"
"Assets/_Game/Scripts/Tool/ExtendedToolDefinition.cs"
"Assets/_Game/Scripts/Tool/ForceToolSelectionDisplay.cs"
"Assets/_Game/Scripts/Tool/ItemDebugger.cs"
"Assets/_Game/Scripts/Tool/ToolModelManager.cs"
"Assets/_Game/Scripts/Tool/ToolSelectionController.cs"
"Assets/_Game/Scripts/Tool/ToolSelectionDebugButton.cs"
"Assets/_Game/Scripts/Tool/ToolSelectionManager.cs"
"Assets/_Game/Scripts/Tool/ToolSelectionUILoader.cs"
"Assets/_Game/Scripts/UI/UIToolkitExtensions.cs"
"Assets/_Game/Scripts/WorldSystems/KinematicPlatform.cs"
"Assets/_Game/Scripts/WorldSystems/ToggleObjectTrigger.cs"
"Assets/_Game/Shaders/Outline/Outline.cs"
"Assets/_Game/Shaders/Selection/Scripts/SelectionColor.cs"
"Assets/_Game/Shaders/TIPS/TIPS.cs"
"Assets/_Game/Shaders/TIPS/TIPS_2.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"