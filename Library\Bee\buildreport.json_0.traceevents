{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754342010643500, "dur": 6646, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754342010650151, "dur": 30062, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754342010680216, "dur": 1325, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010703436, "dur": 10, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010643464, "dur": 33057, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010676523, "dur": 26012, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010676532, "dur": 35, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010676571, "dur": 805, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010677379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010677382, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010677417, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010677421, "dur": 3795, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010681222, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010681288, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010681291, "dur": 23, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010681317, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010681319, "dur": 677, "ph": "X", "name": "ReadAsync 70", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010682002, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010682020, "dur": 6887, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010688916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010688921, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010688951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010688956, "dur": 4301, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010693263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010693266, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754342010693292, "dur": 9236, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010703449, "dur": 35, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 47244640256, "ts": 1754342010643426, "dur": 38125, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754342010681552, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 47244640256, "ts": 1754342010681553, "dur": 29, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010703485, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 42949672960, "ts": 1754342010634034, "dur": 68534, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754342010634142, "dur": 9245, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754342010702577, "dur": 7, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 42949672960, "ts": 1754342010702585, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010703494, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754342010677941, "dur":3786, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010681741, "dur":196, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010681974, "dur":644, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010682715, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":0, "ts":1754342010682661, "dur":113, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010682775, "dur":11637, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010694414, "dur":184, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010694722, "dur":3059, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754342010683472, "dur":341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342010694045, "dur":340, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754342010683814, "dur":10575, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342010683440, "dur":6755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1754342010690197, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342010690345, "dur":4069, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342010683520, "dur":10911, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342010683621, "dur":10788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342010683648, "dur":10770, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342010683724, "dur":10700, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342010683743, "dur":10690, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342010683555, "dur":10855, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342010683591, "dur":10820, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342010683678, "dur":10742, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342010683698, "dur":10723, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342010683773, "dur":10653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342010703036, "dur":374, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 50796780, "ts": 1754342010703521, "dur": 8796, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010712441, "dur": 475, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 50796780, "ts": 1754342010703433, "dur": 9512, "ph": "X", "name": "Write chrome-trace events", "args": {} },
