{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754346478028467, "dur": 8707, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754346478037180, "dur": 263962, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754346478301155, "dur": 6537, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479750307, "dur": 1513, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478025766, "dur": 37774, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478063556, "dur": 1669440, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478065138, "dur": 3928, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478069074, "dur": 2422, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478071501, "dur": 4555, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478076062, "dur": 1182, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478077248, "dur": 1347, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478078602, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478078660, "dur": 540, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346478079204, "dur": 1634323, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479713535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479713539, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479713591, "dur": 1906, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479715500, "dur": 5942, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721450, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721472, "dur": 405, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721881, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479721905, "dur": 230, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754346479722137, "dur": 9955, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479751824, "dur": 144, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 8589934592, "ts": 1754346478022020, "dur": 285723, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754346478307746, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754346478307753, "dur": 1181, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479752005, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 4294967296, "ts": 1754346477980298, "dur": 1753781, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754346477986384, "dur": 20519, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754346479734136, "dur": 5545, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754346479737349, "dur": 32, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754346479739785, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479752011, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754346478062396, "dur":5518, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346478067931, "dur":157, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346478068119, "dur":1471, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346478069652, "dur":110, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346478069762, "dur":1653448, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346479723211, "dur":87, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346479723298, "dur":115, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346479723622, "dur":3715, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754346478069297, "dur":486, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754346478069812, "dur":240876, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754346478321691, "dur":584, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754346478310689, "dur":11591, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754346478322280, "dur":1400950, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754346478069279, "dur":494, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754346478070406, "dur":1185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":2, "ts":1754346478069788, "dur":9400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1754346478081356, "dur":1636947, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":3, "ts":1754346478074488, "dur":1648720, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754346478074494, "dur":1648709, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754346478078437, "dur":1644768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754346478078447, "dur":1644779, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754346478069392, "dur":426, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754346478069818, "dur":252468, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754346478322286, "dur":1400925, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754346478069425, "dur":403, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754346478069828, "dur":1653378, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754346478074530, "dur":1648679, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754346478074564, "dur":1648650, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754346478078515, "dur":1644703, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754346478078544, "dur":1644687, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754346479732906, "dur":455, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 52296160, "ts": 1754346479753771, "dur": 11049, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479764950, "dur": 2313, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 52296160, "ts": 1754346479749439, "dur": 18820, "ph": "X", "name": "Write chrome-trace events", "args": {} },
