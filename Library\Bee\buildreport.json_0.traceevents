{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754339313963841, "dur": 2164, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754339313966023, "dur": 31135, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754339313997159, "dur": 1040, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314020019, "dur": 12, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313963803, "dur": 27794, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313991599, "dur": 27506, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313991608, "dur": 34, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313991646, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313991649, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313992149, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313992177, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313992183, "dur": 3774, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313995961, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313995963, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996000, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996002, "dur": 22, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996029, "dur": 293, "ph": "X", "name": "ReadAsync 146", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996329, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339313996369, "dur": 5035, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339314001407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339314001410, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339314001440, "dur": 8934, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339314010380, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 60129542144, "ts": 1754339314010408, "dur": 8690, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314020032, "dur": 48, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 55834574848, "ts": 1754339313963762, "dur": 34448, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 55834574848, "ts": 1754339313998211, "dur": 26, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314020082, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 51539607552, "ts": 1754339313953046, "dur": 66097, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754339313953171, "dur": 9780, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754339314019147, "dur": 7, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 51539607552, "ts": 1754339314019155, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314020088, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754339313992612, "dur":3579, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339313996198, "dur":319, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339313996554, "dur":445, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339313997025, "dur":76, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339313997102, "dur":14078, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339314011181, "dur":163, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339314011460, "dur":2725, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754339313997372, "dur":4980, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1754339314002354, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339314002485, "dur":8704, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339313997409, "dur":1928, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339314010564, "dur":587, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1754339313999338, "dur":11817, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339313997430, "dur":13752, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339313997476, "dur":13703, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339313997508, "dur":13669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339313997548, "dur":13628, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339313997577, "dur":13648, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339313997609, "dur":13601, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339313997639, "dur":13610, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339313997680, "dur":13538, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339313997726, "dur":13476, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339313997751, "dur":13445, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339314019215, "dur":343, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 49661902, "ts": 1754339314020207, "dur": 4010, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314024375, "dur": 2833, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 49661902, "ts": 1754339314020014, "dur": 7231, "ph": "X", "name": "Write chrome-trace events", "args": {} },
