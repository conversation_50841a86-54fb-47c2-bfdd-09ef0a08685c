{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754343115778504, "dur": 6602, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754343115785110, "dur": 154745, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754343115939867, "dur": 3638, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117555514, "dur": 1352, "ph": "X", "name": "", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115776415, "dur": 16335, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115792753, "dur": 1746132, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115793900, "dur": 2549, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115796454, "dur": 1589, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798046, "dur": 53, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798101, "dur": 534, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798637, "dur": 71, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798714, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115798748, "dur": 277, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115799029, "dur": 7947, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115806980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115806983, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115807015, "dur": 634, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343115807652, "dur": 1711153, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117518815, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117518819, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117518864, "dur": 2441, "ph": "X", "name": "ProcessMessages 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117521309, "dur": 5293, "ph": "X", "name": "ReadAsync 15370", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117526609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117526612, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117526681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117526684, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117527063, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117527132, "dur": 318, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 12884901888, "ts": 1754343117527453, "dur": 10695, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117556871, "dur": 40, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 93864, "tid": 8589934592, "ts": 1754343115772923, "dur": 170617, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754343115943542, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 8589934592, "ts": 1754343115943546, "dur": 1883, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117556912, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 4294967296, "ts": 1754343115738450, "dur": 1801408, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754343115743106, "dur": 16892, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754343117539914, "dur": 4893, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754343117542912, "dur": 31, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 4294967296, "ts": 1754343117544957, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117556920, "dur": 100, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754343115791081, "dur":4357, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343115795454, "dur":198, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343115795691, "dur":775, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343115796509, "dur":74, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343115796583, "dur":1730138, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343117526727, "dur":137, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343117527017, "dur":59, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754343117527152, "dur":4463, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754343115799007, "dur":1727652, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754343115798997, "dur":1727657, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754343115798903, "dur":1727776, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754343115798794, "dur":146647, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754343115956882, "dur":674, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1754343115945442, "dur":12118, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754343115957561, "dur":1569120, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754343115798806, "dur":158758, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754343115957565, "dur":1569109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754343115799018, "dur":1727669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754343115799024, "dur":1727632, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754343115798948, "dur":1727728, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754343115798981, "dur":1727682, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754343115798833, "dur":1727825, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754343115798869, "dur":1727788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754343115799202, "dur":568, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754343115798769, "dur":7028, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":12, "ts":1754343115807666, "dur":1713557, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":0, "ts":1754343117537273, "dur":398, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 51171516, "ts": 1754343117557631, "dur": 10363, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117568155, "dur": 2331, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 51171516, "ts": 1754343117554603, "dur": 16947, "ph": "X", "name": "Write chrome-trace events", "args": {} },
