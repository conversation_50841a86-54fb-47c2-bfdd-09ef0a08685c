{"mod.presencefootsteps.name": "Presence Footsteps", "key.category.presencefootsteps": "Presence Footsteps", "key.presencefootsteps.settings": "Einstellungen öffnen", "key.presencefootsteps.toggle": "Mod ein-/ausschalten", "key.presencefootsteps.toggle.enabled": "Geräusche aktiviert", "key.presencefootsteps.toggle.disabled": "Geräusche deaktiviert", "pf.default_sounds.name": "Standard-Soundpaket", "pf.default_sounds.missing": "Es wurden keine Sound Packs gefunden. Gehe zu Ressourcenpakete und aktiviere %s, um loszulegen.", "menu.pf.title": "Presence-Footsteps-Optionen", "menu.pf.multiplayer.true": "Einzel- & Mehrspieler", "menu.pf.multiplayer.false": "<PERSON><PERSON>spieler", "menu.pf.global.all": "Kreaturen: <PERSON><PERSON>", "menu.pf.global.players_and_hostiles": "Kreaturen: Spieler + Feindliches", "menu.pf.global.players_only": "Kreaturen: <PERSON><PERSON>", "menu.pf.volume.min": "Gesamtlautstärke: AUS", "menu.pf.disable_mod": "Alle Geräusche deaktivieren", "menu.pf.volume": "Gesamtlautstärke: %d%%", "menu.pf.volume.tooltip": "Die Gesamtlautstärke aller Presence-Footsteps-Geräusche. Setze auf 0, wenn du die Mod deaktivieren möchtest.", "menu.pf.volume.hostile_entities": "Feindliche Kreaturen: %d%%", "menu.pf.volume.hostile_entities.tooltip": "Ändert die Lautstärke von feindlichen Laufgeräuschen.", "menu.pf.volume.passive_entities": "Neutrale Kreaturen: %d%%", "menu.pf.volume.passive_entities.tooltip": "Ändert die Lautstärke von Tier-Laufgeräuschen.", "menu.pf.footwear.on": "Schuhwerk: AN", "menu.pf.footwear.off": "Schuhwerk: AUS", "menu.pf.exclusive_mode.on": "Exklusiv-Modus: EIN", "menu.pf.exclusive_mode.off": "Exklusiv-Modus: AUS", "menu.pf.volume.player": "Spieler: %d%%", "menu.pf.volume.player.tooltip": "Ändert die Lautstärke deiner eigenen Schritte.", "menu.pf.volume.other_players": "Andere Spieler: %d%%", "menu.pf.volume.other_players.tooltip": "Ändert die Lautstärke der Laufgeräusche anderer Spieler im Mehrspieler.", "menu.pf.volume.running": "Geschwindigkeitsanpassung: %d%%", "menu.pf.volume.running.tooltip": "Wie stark die Lautstärke der Laufgeräusche beim Rennen variieren kann.\n\nDieser Wert wird zur Gesamtlautstärke HINZUGEFÜGT und skaliert mit deiner Geschwindigkeit.\n\nEs empfiehlt sich, bei kleinen Werten zu starten und langsam anzupassen. Größere Werte verstärken den Effekt.", "menu.pf.volume.wet": "Nasse Oberflächen: %s%%", "menu.pf.volume.wet.tooltip": "Ändert die Lautstärke der Geräusche, die beim Gehen auf nassen Blöcken entstehen.", "menu.pf.volume.foliage": "Grünzeug: %s%%", "menu.pf.volume.foliage.tooltip": "Ändert die Lautstärke der Geräusche, die beim Gehen durch Pflanzen und hohes Gras entstehen.", "menu.pf.stance": "Gangart: %s", "menu.pf.stance.auto": "Automatisch", "menu.pf.stance.none": "<PERSON><PERSON>", "menu.pf.stance.none.tooltip": "Auto wählt basierend auf der 'My Little Pony' Mod eine geeignete Haltung. Wenn diese Mod nicht installiert ist, verhält sich die Einstellung genauso wie ein Zweibeiner.", "menu.pf.stance.quadruped": "<PERSON><PERSON><PERSON><PERSON>", "menu.pf.stance.quadruped.tooltip": "Vierbeiner simuliert eine vierbeinige Gangart.", "menu.pf.stance.biped": "<PERSON><PERSON><PERSON><PERSON>", "menu.pf.stance.biped.tooltip": "Zweibeiner simuliert eine zweibeinige (menschliche) Gangart.", "menu.pf.stance.flying": "Pegasus", "menu.pf.stance.flying.tooltip": "Pegasus simuliert eine vierbeinige Gangart mit Flügeln.", "menu.pf.stance.flying_biped": "<PERSON><PERSON>", "menu.pf.stance.flying_biped.tooltip": "Vogel simuliert eine zweibeinige Gangart mit Flügeln.", "menu.pf.report.full": "Kompletter Bericht", "menu.pf.report.concise": "<PERSON><PERSON><PERSON>", "menu.pf.report.acoustics": "Dump Acoustics", "menu.pf.group.volume": "Lautstärke", "menu.pf.group.footsteps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu.pf.group.debugging": "Debugging", "menu.pf.group.sound_packs": "Geräuschpakete", "pf.report.save": "Report gespeichert unter: %s", "pf.report.error": "Es konnte kein Report erstellt werden: %s", "pf.update.title": "Ein Update ist verfügbar", "pf.update.text": "Presence Footsteps %s", "pf.update.checking": "Such<PERSON> nach Updates...", "pf.update.updates_available": "Ein Update ist verfügbar!\n Presence Footsteps %s für MC%s", "pf.update.up_to_date": "Auf dem neusten Stand", "subtitles.pf.footsteps": "<PERSON><PERSON><PERSON>", "subtitles.pf.wood_squeak": "<PERSON><PERSON><PERSON>", "subtitles.pf.wood_bends": "Holz biegt sich", "subtitles.pf.wood_creaks": "<PERSON><PERSON><PERSON> knarrt", "subtitles.pf.swim": "<PERSON><PERSON> fließt", "subtitles.pf.wings_flap": "Flügelschlag", "subtitles.pf.leaves_rustle": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "subtitles.pf.grass_rustles": "Gras raschelt", "subtitles.pf.fire_flames": "Flammen zischen", "subtitles.pf.land": "<PERSON><PERSON><PERSON><PERSON> falten sich", "subtitles.pf.metal_clink": "<PERSON>l klirrt", "subtitles.pf.snow_crunch": "Schn<PERSON> knirscht", "subtitles.pf.sand_crunch": "Sand knirscht", "subtitles.pf.grass_crunch": "Gras knirscht", "subtitles.pf.gravel.crunch": "<PERSON><PERSON> kn<PERSON>", "subtitles.pf.ice_crack": "<PERSON><PERSON> bricht", "subtitles.pf.mud_squealk": "<PERSON>hl<PERSON><PERSON> matscht", "subtitles.pf.glass_rattles": "<PERSON><PERSON> k<PERSON>t", "subtitles.pf.glass_croak": "Glas stöhnt", "subtitles.pf.chain": "<PERSON>tten klimpern", "subtitles.pf.stone_crack": "<PERSON>", "subtitles.pf.wax_squeak": "<PERSON>achs quietscht"}