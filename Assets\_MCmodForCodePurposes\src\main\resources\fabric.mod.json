{"schemaVersion": 1, "id": "presencefootsteps", "version": "${version}", "name": "Presence Footsteps", "description": "An overly complicated sound mod.", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON> (Ha3)", "<PERSON><PERSON>"], "contact": {"sources": "https://github.com/Sollace/Presence-Footsteps", "issues": "https://github.com/Sollace/Presence-Footsteps/issues"}, "license": "MIT", "icon": "logo.png", "environment": "client", "entrypoints": {"client": ["eu.ha3.presencefootsteps.PresenceFootsteps"], "modmenu": ["eu.ha3.presencefootsteps.PFModMenuFactory"]}, "mixins": ["presencefootsteps.mixin.json"], "depends": {"minecraft": "${minecraftVersion}", "fabricloader": "${loaderVersion}", "java": ">=21", "fabric-api": "${fabricVersion}", "kirin": "${kirinVersion}"}, "suggests": {"minelp": "${minelpVersion}"}, "custom": {"modmenu": {"links": {"modmenu.discord": "https://discord.gg/zKSZ8Mg", "modmenu.github_releases": "https://github.com/Sollace/Presence-Footsteps/releases", "modmenu.modrinth": "https://modrinth.com/mod/presence-footsteps"}, "update_checker": true}}}