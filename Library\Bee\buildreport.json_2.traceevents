{ "pid": 93864, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026046980, "dur": 26052, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073033, "dur": 330771, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073052, "dur": 94, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073149, "dur": 533, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073687, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073726, "dur": 5, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026073733, "dur": 3171, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026076909, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026076958, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026076960, "dur": 34, "ph": "X", "name": "ReadAsync 553", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026076996, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026076998, "dur": 33, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077035, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077037, "dur": 34, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077073, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077075, "dur": 24, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077101, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077122, "dur": 23, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077149, "dur": 56, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077208, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077211, "dur": 41, "ph": "X", "name": "ReadAsync 491", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077254, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077257, "dur": 41, "ph": "X", "name": "ReadAsync 481", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077301, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077304, "dur": 34, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077341, "dur": 29, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077372, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077375, "dur": 29, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077405, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077407, "dur": 68, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077480, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077515, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077517, "dur": 37, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077557, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077559, "dur": 26, "ph": "X", "name": "ReadAsync 136", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077588, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077590, "dur": 28, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077622, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077654, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077657, "dur": 24, "ph": "X", "name": "ReadAsync 700", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077684, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077686, "dur": 93, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077782, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077815, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077817, "dur": 27, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077847, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077849, "dur": 22, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077874, "dur": 24, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077900, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077902, "dur": 24, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077928, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077931, "dur": 39, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077971, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077973, "dur": 20, "ph": "X", "name": "ReadAsync 854", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026077996, "dur": 33, "ph": "X", "name": "ReadAsync 191", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078032, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078034, "dur": 23, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078060, "dur": 30, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078093, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078095, "dur": 20, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078116, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078118, "dur": 23, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078143, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078146, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078168, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078170, "dur": 26, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078197, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078200, "dur": 28, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078230, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078231, "dur": 30, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078263, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078266, "dur": 29, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078298, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078300, "dur": 20, "ph": "X", "name": "ReadAsync 623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078321, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078322, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078344, "dur": 25, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078370, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078372, "dur": 26, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078401, "dur": 23, "ph": "X", "name": "ReadAsync 598", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078427, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078448, "dur": 17, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078468, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078500, "dur": 20, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078522, "dur": 18, "ph": "X", "name": "ReadAsync 557", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078542, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078569, "dur": 20, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078590, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078592, "dur": 24, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078619, "dur": 19, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078641, "dur": 24, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078666, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078668, "dur": 20, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078691, "dur": 22, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078716, "dur": 23, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078741, "dur": 22, "ph": "X", "name": "ReadAsync 221", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078765, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078767, "dur": 43, "ph": "X", "name": "ReadAsync 478", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078813, "dur": 28, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078843, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078845, "dur": 25, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078872, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078874, "dur": 15, "ph": "X", "name": "ReadAsync 552", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078892, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078915, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078917, "dur": 20, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078939, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078941, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078959, "dur": 21, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078982, "dur": 1, "ph": "X", "name": "ProcessMessages 218", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026078985, "dur": 31, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079017, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079019, "dur": 16, "ph": "X", "name": "ReadAsync 595", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079039, "dur": 23, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079065, "dur": 27, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079095, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079118, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079142, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079144, "dur": 24, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079170, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079190, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079193, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079217, "dur": 19, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079238, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079274, "dur": 23, "ph": "X", "name": "ReadAsync 527", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079299, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079301, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079328, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079330, "dur": 26, "ph": "X", "name": "ReadAsync 201", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079358, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079360, "dur": 27, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079388, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079390, "dur": 28, "ph": "X", "name": "ReadAsync 419", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079420, "dur": 20, "ph": "X", "name": "ReadAsync 557", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079443, "dur": 24, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079468, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079470, "dur": 24, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079497, "dur": 22, "ph": "X", "name": "ReadAsync 755", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079521, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079523, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079552, "dur": 1, "ph": "X", "name": "ProcessMessages 740", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079554, "dur": 19, "ph": "X", "name": "ReadAsync 740", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079574, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079576, "dur": 27, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079605, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079630, "dur": 22, "ph": "X", "name": "ReadAsync 574", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079656, "dur": 20, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079679, "dur": 26, "ph": "X", "name": "ReadAsync 251", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079707, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079710, "dur": 25, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079736, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079738, "dur": 19, "ph": "X", "name": "ReadAsync 781", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079759, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079761, "dur": 24, "ph": "X", "name": "ReadAsync 74", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079787, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079789, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079813, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079815, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079838, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079839, "dur": 26, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079868, "dur": 23, "ph": "X", "name": "ReadAsync 436", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079894, "dur": 22, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079919, "dur": 23, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079944, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079945, "dur": 22, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079970, "dur": 22, "ph": "X", "name": "ReadAsync 462", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079995, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026079997, "dur": 39, "ph": "X", "name": "ReadAsync 495", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080038, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080040, "dur": 24, "ph": "X", "name": "ReadAsync 875", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080066, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080088, "dur": 21, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080111, "dur": 24, "ph": "X", "name": "ReadAsync 127", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080138, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080139, "dur": 20, "ph": "X", "name": "ReadAsync 622", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080162, "dur": 20, "ph": "X", "name": "ReadAsync 464", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080185, "dur": 20, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080208, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080232, "dur": 23, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080258, "dur": 24, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080285, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080308, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080310, "dur": 35, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080348, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080350, "dur": 19, "ph": "X", "name": "ReadAsync 615", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080370, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080372, "dur": 29, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080405, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080439, "dur": 23, "ph": "X", "name": "ReadAsync 578", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080464, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080467, "dur": 27, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080496, "dur": 22, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080520, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080522, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080549, "dur": 23, "ph": "X", "name": "ReadAsync 594", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080573, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080575, "dur": 23, "ph": "X", "name": "ReadAsync 611", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080601, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080603, "dur": 23, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080629, "dur": 21, "ph": "X", "name": "ReadAsync 693", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080654, "dur": 22, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080679, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080681, "dur": 33, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080718, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080742, "dur": 24, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080768, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080770, "dur": 26, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080798, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080800, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080823, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080825, "dur": 19, "ph": "X", "name": "ReadAsync 669", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080845, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080847, "dur": 25, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080874, "dur": 26, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080902, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080904, "dur": 25, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080932, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080934, "dur": 19, "ph": "X", "name": "ReadAsync 587", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080954, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080956, "dur": 20, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080978, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026080980, "dur": 26, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081010, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081034, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081036, "dur": 19, "ph": "X", "name": "ReadAsync 623", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081056, "dur": 1, "ph": "X", "name": "ProcessMessages 106", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081058, "dur": 28, "ph": "X", "name": "ReadAsync 106", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081089, "dur": 22, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081113, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081114, "dur": 24, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081140, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081142, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081167, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081169, "dur": 21, "ph": "X", "name": "ReadAsync 646", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081192, "dur": 23, "ph": "X", "name": "ReadAsync 662", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081217, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081219, "dur": 17, "ph": "X", "name": "ReadAsync 599", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081239, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081259, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081261, "dur": 32, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081295, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081297, "dur": 24, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081322, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081324, "dur": 27, "ph": "X", "name": "ReadAsync 606", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081354, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081356, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081382, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081403, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081404, "dur": 27, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081433, "dur": 19, "ph": "X", "name": "ReadAsync 506", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081454, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081456, "dur": 25, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081483, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081485, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081507, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081509, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081534, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081559, "dur": 21, "ph": "X", "name": "ReadAsync 614", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081583, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081585, "dur": 29, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081616, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081618, "dur": 31, "ph": "X", "name": "ReadAsync 648", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081652, "dur": 24, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081677, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081679, "dur": 30, "ph": "X", "name": "ReadAsync 667", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081711, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081714, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081739, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081742, "dur": 21, "ph": "X", "name": "ReadAsync 691", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081765, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081767, "dur": 27, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081795, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081797, "dur": 22, "ph": "X", "name": "ReadAsync 181", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081822, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081824, "dur": 22, "ph": "X", "name": "ReadAsync 205", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081848, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081850, "dur": 20, "ph": "X", "name": "ReadAsync 673", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081874, "dur": 38, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081915, "dur": 25, "ph": "X", "name": "ReadAsync 583", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081941, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081943, "dur": 23, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081968, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026081996, "dur": 26, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082024, "dur": 21, "ph": "X", "name": "ReadAsync 713", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082047, "dur": 19, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082069, "dur": 21, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082092, "dur": 18, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082112, "dur": 21, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082135, "dur": 24, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082162, "dur": 21, "ph": "X", "name": "ReadAsync 655", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082185, "dur": 25, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082213, "dur": 19, "ph": "X", "name": "ReadAsync 674", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082233, "dur": 21, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082257, "dur": 19, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082277, "dur": 20, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082300, "dur": 32, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082335, "dur": 23, "ph": "X", "name": "ReadAsync 829", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082359, "dur": 22, "ph": "X", "name": "ReadAsync 718", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082383, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082407, "dur": 19, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082428, "dur": 19, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082449, "dur": 26, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082477, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082499, "dur": 23, "ph": "X", "name": "ReadAsync 161", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082524, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082544, "dur": 18, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082563, "dur": 24, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082589, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082611, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082634, "dur": 20, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082655, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082681, "dur": 24, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082708, "dur": 20, "ph": "X", "name": "ReadAsync 653", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082729, "dur": 19, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082750, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082769, "dur": 23, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082794, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082816, "dur": 21, "ph": "X", "name": "ReadAsync 511", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082839, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082863, "dur": 22, "ph": "X", "name": "ReadAsync 594", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082887, "dur": 23, "ph": "X", "name": "ReadAsync 522", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082913, "dur": 24, "ph": "X", "name": "ReadAsync 523", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082939, "dur": 19, "ph": "X", "name": "ReadAsync 609", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082960, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026082981, "dur": 22, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083005, "dur": 23, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083031, "dur": 21, "ph": "X", "name": "ReadAsync 679", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083054, "dur": 23, "ph": "X", "name": "ReadAsync 620", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083079, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083102, "dur": 20, "ph": "X", "name": "ReadAsync 593", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083123, "dur": 22, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083147, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083167, "dur": 19, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083189, "dur": 21, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083211, "dur": 48, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083262, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083265, "dur": 31, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083297, "dur": 1, "ph": "X", "name": "ProcessMessages 1124", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083300, "dur": 23, "ph": "X", "name": "ReadAsync 1124", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083326, "dur": 26, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083356, "dur": 20, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083378, "dur": 24, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083404, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083406, "dur": 28, "ph": "X", "name": "ReadAsync 647", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083436, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083439, "dur": 28, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083468, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083470, "dur": 24, "ph": "X", "name": "ReadAsync 663", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083496, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083497, "dur": 24, "ph": "X", "name": "ReadAsync 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083524, "dur": 23, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083549, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083552, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083586, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083588, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083610, "dur": 18, "ph": "X", "name": "ReadAsync 359", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083630, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083657, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083659, "dur": 26, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083687, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083688, "dur": 26, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083716, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083718, "dur": 27, "ph": "X", "name": "ReadAsync 633", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083747, "dur": 21, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083771, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083773, "dur": 29, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083803, "dur": 1, "ph": "X", "name": "ProcessMessages 827", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083805, "dur": 16, "ph": "X", "name": "ReadAsync 827", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083824, "dur": 20, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083846, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083849, "dur": 19, "ph": "X", "name": "ReadAsync 194", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083871, "dur": 21, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083895, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083897, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083921, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083924, "dur": 21, "ph": "X", "name": "ReadAsync 679", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083948, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083949, "dur": 21, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083974, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083997, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026083999, "dur": 32, "ph": "X", "name": "ReadAsync 216", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084033, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084034, "dur": 27, "ph": "X", "name": "ReadAsync 715", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084063, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084065, "dur": 23, "ph": "X", "name": "ReadAsync 727", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084091, "dur": 23, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084117, "dur": 22, "ph": "X", "name": "ReadAsync 674", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084142, "dur": 24, "ph": "X", "name": "ReadAsync 671", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084169, "dur": 21, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084192, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084193, "dur": 20, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084217, "dur": 25, "ph": "X", "name": "ReadAsync 507", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084243, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084245, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084270, "dur": 20, "ph": "X", "name": "ReadAsync 639", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084292, "dur": 21, "ph": "X", "name": "ReadAsync 667", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084316, "dur": 19, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084338, "dur": 20, "ph": "X", "name": "ReadAsync 666", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084360, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084361, "dur": 22, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084385, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084387, "dur": 20, "ph": "X", "name": "ReadAsync 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084411, "dur": 22, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084435, "dur": 22, "ph": "X", "name": "ReadAsync 706", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084459, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084460, "dur": 26, "ph": "X", "name": "ReadAsync 670", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084488, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084490, "dur": 25, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084518, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084520, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084545, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084547, "dur": 24, "ph": "X", "name": "ReadAsync 631", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084575, "dur": 26, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084604, "dur": 21, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084628, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084650, "dur": 26, "ph": "X", "name": "ReadAsync 195", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084679, "dur": 19, "ph": "X", "name": "ReadAsync 663", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084700, "dur": 19, "ph": "X", "name": "ReadAsync 738", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084722, "dur": 23, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084748, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084750, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084776, "dur": 24, "ph": "X", "name": "ReadAsync 692", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084802, "dur": 19, "ph": "X", "name": "ReadAsync 678", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084822, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084824, "dur": 22, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084848, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084873, "dur": 21, "ph": "X", "name": "ReadAsync 756", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084896, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084898, "dur": 24, "ph": "X", "name": "ReadAsync 728", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084923, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084925, "dur": 18, "ph": "X", "name": "ReadAsync 707", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084956, "dur": 25, "ph": "X", "name": "ReadAsync 45", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084982, "dur": 1, "ph": "X", "name": "ProcessMessages 1371", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026084984, "dur": 20, "ph": "X", "name": "ReadAsync 1371", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085007, "dur": 21, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085031, "dur": 19, "ph": "X", "name": "ReadAsync 653", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085052, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085076, "dur": 22, "ph": "X", "name": "ReadAsync 632", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085099, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085102, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085124, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085126, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085148, "dur": 22, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085173, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085195, "dur": 19, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085216, "dur": 23, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085241, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085243, "dur": 28, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085274, "dur": 19, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085295, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085297, "dur": 20, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085319, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085341, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085343, "dur": 30, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085376, "dur": 42, "ph": "X", "name": "ReadAsync 553", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085421, "dur": 29, "ph": "X", "name": "ReadAsync 118", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085452, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085453, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085471, "dur": 16, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085489, "dur": 16, "ph": "X", "name": "ReadAsync 174", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085508, "dur": 16, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085526, "dur": 17, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085545, "dur": 21, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085568, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085590, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085608, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085625, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085644, "dur": 15, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085661, "dur": 14, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085678, "dur": 16, "ph": "X", "name": "ReadAsync 45", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085695, "dur": 17, "ph": "X", "name": "ReadAsync 202", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085714, "dur": 16, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085732, "dur": 21, "ph": "X", "name": "ReadAsync 212", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085755, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085779, "dur": 1, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026085781, "dur": 29895, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026115685, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026115689, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026115739, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026115742, "dur": 388, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116137, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116178, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116181, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116204, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116263, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116307, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116309, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116343, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116345, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116372, "dur": 306, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116683, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116715, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116717, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116759, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116794, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116796, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116868, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116897, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116957, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116985, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026116987, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117014, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117016, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117067, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117101, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117103, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117157, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117187, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117189, "dur": 153, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117347, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117385, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117388, "dur": 209, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117602, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117633, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117635, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117702, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117734, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117736, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117783, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117810, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117812, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117865, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117898, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026117900, "dur": 105, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118010, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118043, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118078, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118107, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118109, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118143, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118329, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118360, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118362, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118412, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118439, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118473, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118507, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118508, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118554, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118584, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118587, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118622, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118624, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118769, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118801, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118804, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118916, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118948, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026118950, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119056, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119085, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119087, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119198, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119227, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119229, "dur": 146, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119380, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119473, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119484, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119511, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119540, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119542, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119591, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119621, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119792, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119821, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119878, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119911, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119940, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026119990, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120067, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120107, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120250, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120283, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120315, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120352, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120379, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120418, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120449, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120475, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120502, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120504, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120533, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120535, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120568, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120596, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120686, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120718, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120720, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120889, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120921, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026120923, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121010, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121044, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121046, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121084, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121116, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121151, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121187, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121189, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121221, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121223, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121278, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121310, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121430, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121460, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121635, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121662, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121740, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121773, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121775, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121807, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121810, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121840, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121842, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121872, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121903, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121905, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121961, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026121994, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122027, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122134, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122169, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122285, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122320, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122423, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122458, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122460, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122494, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122496, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122527, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122529, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122560, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122562, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122607, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122638, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122640, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122678, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122680, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122715, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122789, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122823, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122825, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122923, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026122955, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123104, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123133, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123134, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123173, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123201, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123234, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123235, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123267, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123269, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123302, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123341, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123373, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123427, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123466, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123611, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123642, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123644, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123792, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123824, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123826, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123870, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123904, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123944, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123973, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026123975, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124042, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124074, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124076, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124177, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124212, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124399, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124433, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124496, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124524, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124554, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124601, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124634, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124636, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124702, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124739, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124741, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124770, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124810, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124837, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124871, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124874, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124968, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026124999, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125184, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125220, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125222, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125269, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125300, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125302, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125349, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125373, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125376, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125407, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125432, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125434, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125459, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125461, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125490, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125492, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125522, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125615, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125737, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125739, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125766, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125768, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125792, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125940, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125970, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026125973, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126035, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126064, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126065, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126092, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126094, "dur": 193, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126292, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126320, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126345, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126388, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126418, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126543, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126571, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126639, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126669, "dur": 107, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126781, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126811, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126838, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126840, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126866, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126868, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026126980, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127003, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127029, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127059, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127060, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127158, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127187, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127188, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127214, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127303, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127331, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127333, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127361, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127363, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127536, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127569, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026127572, "dur": 532, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026128108, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026128138, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026128141, "dur": 2996, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026131140, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026131143, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026131180, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026131183, "dur": 3314, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026134501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026134504, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026134525, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026134527, "dur": 6234, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026140771, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026140776, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026140807, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026140811, "dur": 5109, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026145926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026145929, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026145953, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026145956, "dur": 553, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026146515, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026146543, "dur": 4645, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151196, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151241, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151244, "dur": 493, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151741, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151776, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026151779, "dur": 539, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026152323, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026152356, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026152358, "dur": 5850, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026158215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026158220, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026158263, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026158267, "dur": 4995, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026163267, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026163270, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026163317, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026163321, "dur": 4338, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026167665, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026167669, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026167701, "dur": 15313, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183023, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183028, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183064, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183183, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183221, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183223, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183319, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183351, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183449, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183480, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183482, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183573, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183603, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183870, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026183905, "dur": 434, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026184344, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026184377, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026184688, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026184721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026184723, "dur": 388, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185116, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185148, "dur": 381, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185533, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185536, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185568, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026185570, "dur": 467, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186042, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186080, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186083, "dur": 520, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186609, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186646, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186669, "dur": 167, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186840, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186875, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026186877, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187005, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187008, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187092, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187151, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187189, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026187191, "dur": 871, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188069, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188107, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188152, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188236, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188264, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188458, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188488, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188602, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188620, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188745, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188770, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188836, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188860, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188909, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026188935, "dur": 550, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189491, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189517, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189547, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189578, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189845, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189878, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026189895, "dur": 532, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026190431, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026190433, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026190473, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026190892, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026190911, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191001, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191027, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191038, "dur": 382, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191425, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191441, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191442, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191494, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191525, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191527, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191576, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191603, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191619, "dur": 25, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191646, "dur": 5, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191653, "dur": 63, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191721, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191748, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191754, "dur": 167, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191926, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191957, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026191963, "dur": 213, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192181, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192212, "dur": 7, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192220, "dur": 29, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192253, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192281, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192380, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192404, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192484, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192513, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192514, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192677, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192720, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192768, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026192804, "dur": 540, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193349, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193382, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193574, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193605, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193607, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193795, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026193827, "dur": 234, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194066, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194108, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194151, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194177, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194246, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194272, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194308, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194332, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194357, "dur": 393, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194756, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194786, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194919, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194950, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026194952, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195040, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195068, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195071, "dur": 70, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195145, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195170, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195172, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195313, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195341, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195369, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195434, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195461, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195463, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195489, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195615, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195632, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195681, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195706, "dur": 12, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195719, "dur": 221, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195944, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195970, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026195972, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196010, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196040, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196079, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196107, "dur": 165, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196276, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196301, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196475, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196504, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196506, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196533, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196536, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196565, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196592, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196594, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196823, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196850, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026196945, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197030, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197032, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197067, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197092, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197093, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197193, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197221, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197247, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197249, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197404, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197433, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197503, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197532, "dur": 110, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197647, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197679, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197725, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197727, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197816, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197844, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197877, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026197904, "dur": 141, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198050, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198080, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198109, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198180, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198208, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198210, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198239, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198263, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198283, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198615, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198646, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198649, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198680, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198682, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198791, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198808, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198892, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198894, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198920, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026198978, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199005, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199007, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199157, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199189, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199191, "dur": 302, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199499, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199528, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199530, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199596, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199623, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199653, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199680, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199727, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199755, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199830, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199860, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199862, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199915, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026199946, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200031, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200058, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200061, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200086, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200088, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200151, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200190, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200192, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200222, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200224, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200326, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200352, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200353, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200675, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200709, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200711, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200745, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200747, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200811, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200846, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200874, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026200876, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201134, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201163, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201165, "dur": 422, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201591, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201622, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201624, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201819, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201847, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201983, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026201985, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202016, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202018, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202302, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202330, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202332, "dur": 120, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202457, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202489, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202492, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202514, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202566, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202594, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202598, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202638, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202640, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202751, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026202782, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203163, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203192, "dur": 492, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203689, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203719, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203721, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203772, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203802, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203804, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203912, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026203940, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204001, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204030, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204048, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204151, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204179, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204211, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204244, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204372, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204401, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204402, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204431, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204472, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204501, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204503, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204648, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204677, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204679, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204731, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204889, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204920, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026204923, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205021, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205051, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205122, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205151, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205153, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205258, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205288, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205396, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205398, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205428, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205486, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205511, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205513, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205543, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205545, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205579, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205581, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205610, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205647, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205649, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205679, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205704, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205745, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205772, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205810, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205830, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205831, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205860, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205890, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026205921, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206138, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206139, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206174, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206237, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206269, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206271, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206367, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206397, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206399, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206428, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206430, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206482, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206509, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206623, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206649, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026206651, "dur": 795, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207452, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207477, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207578, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207607, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207609, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207643, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207645, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207698, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207727, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207729, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207759, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207762, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207830, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207861, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207863, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207944, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207970, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026207972, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208003, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208005, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208034, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208056, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208145, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208179, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208210, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208242, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208244, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208294, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208320, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208322, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208361, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208390, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208483, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208511, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208513, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208546, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208578, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208605, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208710, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208745, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208747, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208777, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208801, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208803, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208829, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208868, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208903, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208953, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208982, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026208984, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209012, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209041, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209066, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209070, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209114, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209143, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209144, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209267, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209299, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209680, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209718, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209720, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209774, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209802, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209910, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209941, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209970, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026209994, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210030, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210054, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210056, "dur": 385, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210446, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210472, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210541, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210575, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210857, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210882, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026210939, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211009, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211011, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211042, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211045, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211119, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211150, "dur": 370, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211526, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211558, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211560, "dur": 228, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211793, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026211822, "dur": 353, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212179, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212228, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212254, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212256, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212375, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212404, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212406, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212436, "dur": 198, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212639, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212669, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212671, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212707, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212733, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212760, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212762, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212791, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026212793, "dur": 269, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213074, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213184, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213186, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213219, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213220, "dur": 87, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213313, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213341, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213396, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213426, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213428, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213458, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213477, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213683, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213709, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213748, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213775, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213799, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213911, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213944, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026213947, "dur": 212, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214164, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214193, "dur": 239, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214437, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214465, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214467, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214497, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214553, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214582, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214584, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214613, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214614, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214642, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214802, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214827, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214829, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214863, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026214867, "dur": 605, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215475, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215477, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215506, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215508, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215807, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215834, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215838, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215885, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215917, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026215920, "dur": 131, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216055, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216083, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216085, "dur": 64, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216154, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216179, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216181, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216210, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216212, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216246, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216284, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216311, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216313, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216395, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216414, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026216416, "dur": 1052, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217473, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217502, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217505, "dur": 195, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217705, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217734, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026217737, "dur": 2439, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026220185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026220189, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026220220, "dur": 12, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026220234, "dur": 2162, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026222408, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026222416, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026222468, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026222486, "dur": 7085, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026229582, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026229589, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026229646, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026229661, "dur": 569, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026230235, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026230286, "dur": 33, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026230321, "dur": 8, "ph": "X", "name": "ProcessMessages 10", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026230331, "dur": 1153, "ph": "X", "name": "ReadAsync 10", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231489, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231491, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231533, "dur": 10, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231544, "dur": 78, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231627, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231659, "dur": 4, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026231665, "dur": 20127, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026251803, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026251808, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026251868, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026251886, "dur": 94749, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026346645, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026346650, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026346687, "dur": 24, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026346713, "dur": 37715, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026384436, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026384440, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026384487, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026384501, "dur": 6015, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026390521, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026390525, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026390565, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026390568, "dur": 1385, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026391959, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026392006, "dur": 10, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026392017, "dur": 413, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026392435, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026392467, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 73014444032, "ts": 1754342026392469, "dur": 11327, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 50798507, "ts": 1754342026404825, "dur": 2870, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 68719476736, "ts": 1754342026037058, "dur": 366782, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754342026037144, "dur": 9771, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754342026403844, "dur": 9, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 68719476736, "ts": 1754342026403854, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 50798507, "ts": 1754342026407698, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754342025827022, "dur": 1678, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754342025828702, "dur": 206783, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754342026035488, "dur": 1550, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 50798507, "ts": 1754342026407710, "dur": 6, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754342026073707, "dur":121, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026073859, "dur":2893, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026076763, "dur":493, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026077344, "dur":343, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026078319, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":0, "ts":1754342026086190, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":0, "ts":1754342026077710, "dur":8879, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026086596, "dur":306235, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026392832, "dur":231, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026393250, "dur":3270, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754342026078069, "dur":8534, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026092872, "dur":444, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754342026093316, "dur":1693, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754342026095009, "dur":15458, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754342026110467, "dur":499, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754342026110966, "dur":4281, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754342026115247, "dur":189, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754342026115436, "dur":366, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754342026115802, "dur":312, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754342026116114, "dur":90, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754342026116205, "dur":56, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754342026086611, "dur":29669, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026116291, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754342026116353, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026116658, "dur":670, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026117334, "dur":160, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026117496, "dur":198, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026117700, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026117882, "dur":166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026118054, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026118277, "dur":460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026118739, "dur":488, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026119229, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026119431, "dur":1225, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026120658, "dur":271, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026120931, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026121103, "dur":488, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026121593, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026121817, "dur":259, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026122078, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026122255, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026122449, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026122629, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026122839, "dur":451, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026123292, "dur":932, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026124227, "dur":162, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026124395, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026124581, "dur":235, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026124818, "dur":318, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026125138, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026125311, "dur":2279, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026127596, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026127798, "dur":236, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026128035, "dur":610, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026128646, "dur":293, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026128941, "dur":298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026129241, "dur":620, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026129863, "dur":426, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026130290, "dur":187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026130478, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026131249, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754342026131399, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754342026131583, "dur":162, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754342026131747, "dur":262, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754342026116469, "dur":15916, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754342026132386, "dur":14933, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026156003, "dur":12158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754342026168162, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026169699, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026169812, "dur":13978, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026183810, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026184000, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026184140, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026184267, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026184434, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026184964, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026185375, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026185781, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026186200, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026186660, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026187436, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026187677, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026188028, "dur":847, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026188887, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026189071, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026189273, "dur":151, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":1, "ts":1754342026189426, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754342026189479, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026190130, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026190749, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754342026190884, "dur":816, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026192074, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026193480, "dur":1124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026196039, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026196270, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026196436, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Antlr3.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754342026196492, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026198100, "dur":187, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026198296, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026198637, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualScripting.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754342026198696, "dur":787, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026200618, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026200942, "dur":612, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026202706, "dur":857, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026204774, "dur":411, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026205195, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026205510, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026206221, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026206698, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026208413, "dur":357, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026208778, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026209092, "dur":312, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026210355, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026212328, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754342026212420, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026213672, "dur":277, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026213957, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026214600, "dur":847, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754342026216632, "dur":4372, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":1, "ts":1754342026221156, "dur":171667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026078104, "dur":8517, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026086628, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026087268, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026087987, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026088762, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026089479, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026090083, "dur":753, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026090837, "dur":724, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026091561, "dur":755, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026092322, "dur":870, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026093192, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026093982, "dur":983, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026094965, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754342026095731, "dur":900, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026096631, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026097310, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026097945, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026098643, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026099298, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026099984, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026100663, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1754342026086628, "dur":14712, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026101340, "dur":2849, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026104190, "dur":5519, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026109709, "dur":5002, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026114711, "dur":1656, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026116374, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026117086, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Drawing-FeaturesChecked.txt_vy29.info" }}
,{ "pid":12345, "tid":2, "ts":1754342026117170, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026117887, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026118548, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026119295, "dur":1038, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026120338, "dur":960, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026121304, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026121978, "dur":724, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026122707, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026123428, "dur":618, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026124053, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026124787, "dur":729, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026125523, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026126245, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":2, "ts":1754342026126438, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026126593, "dur":650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":2, "ts":1754342026127243, "dur":426, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026128933, "dur":61750, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":2, "ts":1754342026190917, "dur":316, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026192004, "dur":236, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026193465, "dur":924, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026195435, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026195574, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026195866, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026196267, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026196792, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026199169, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026200914, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026202637, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026203925, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026205315, "dur":394, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026205716, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026206418, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026207798, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026208989, "dur":329, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026209325, "dur":219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026209552, "dur":328, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026209887, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026210133, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026212104, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026213535, "dur":349, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026213923, "dur":345, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026214671, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026216560, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754342026218292, "dur":14157, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":2, "ts":1754342026232552, "dur":160289, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026078087, "dur":8523, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026087014, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026087817, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1754342026088566, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":3, "ts":1754342026089217, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026089958, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026090731, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":3, "ts":1754342026091435, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026092249, "dur":783, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026093032, "dur":848, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1754342026093880, "dur":965, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026094845, "dur":779, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026095624, "dur":889, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026096514, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026097233, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026097894, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026098547, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026099216, "dur":688, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026086622, "dur":13282, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026099905, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026100578, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1754342026101257, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":3, "ts":1754342026099905, "dur":4588, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026104493, "dur":5957, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026110451, "dur":3416, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026113868, "dur":2414, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026116336, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026116959, "dur":256, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026117227, "dur":148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":3, "ts":1754342026117478, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026117669, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026117830, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026118010, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026118222, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026118400, "dur":384, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026118786, "dur":435, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026119223, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026119437, "dur":924, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026120363, "dur":322, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026120882, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026121058, "dur":515, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026121575, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026121794, "dur":252, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026122052, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026122237, "dur":186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026122429, "dur":203, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026122634, "dur":224, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026122863, "dur":436, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026123301, "dur":945, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026124248, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026124413, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026124620, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026124810, "dur":320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026125133, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026125320, "dur":2280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026127602, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026127826, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026128050, "dur":582, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026128634, "dur":294, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026128935, "dur":295, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026129235, "dur":597, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026129835, "dur":444, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026130281, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026130467, "dur":307, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":3, "ts":1754342026116948, "dur":14318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026131876, "dur":70, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754342026132854, "dur":214567, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026391162, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026391051, "dur":258, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":3, "ts":1754342026391328, "dur":1458, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754342026078120, "dur":8509, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026086636, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026087297, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026088053, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026088737, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026089479, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026090007, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026090763, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026091486, "dur":808, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026092295, "dur":860, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026093156, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026093947, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026094904, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026095666, "dur":901, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026096567, "dur":673, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026097240, "dur":661, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026097901, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026098605, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026099277, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026099975, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026100603, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1754342026086636, "dur":14668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026101305, "dur":3347, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026104653, "dur":5900, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026110554, "dur":2632, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026113187, "dur":3097, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026116304, "dur":772, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026117076, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Runtime.Serialization-FeaturesChecked.txt_37nq.info" }}
,{ "pid":12345, "tid":4, "ts":1754342026117135, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026117833, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026118524, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026119233, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026120035, "dur":844, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026120887, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026121509, "dur":733, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026122250, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026122954, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026123621, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026124245, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026124998, "dur":787, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026125792, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026126427, "dur":173, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Transactions-FeaturesChecked.txt_ugsj.info" }}
,{ "pid":12345, "tid":4, "ts":1754342026126624, "dur":588, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":4, "ts":1754342026127213, "dur":435, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026127653, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":4, "ts":1754342026127779, "dur":192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026127979, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026159010, "dur":33742, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":4, "ts":1754342026192945, "dur":637, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026194968, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026195176, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754342026195422, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026197235, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026197760, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClusterInputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754342026198488, "dur":507, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026199032, "dur":567, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026200556, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026200878, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026201066, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026202724, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026204464, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026205965, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026206569, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754342026206654, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026208171, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026208555, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026208867, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026209226, "dur":538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026210899, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026213018, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026213229, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026213460, "dur":419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026213922, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026214728, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026216597, "dur":502, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754342026218522, "dur":13827, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":4, "ts":1754342026232519, "dur":160328, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026078152, "dur":8484, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026086642, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026087418, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026088142, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026088855, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026089568, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026090304, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026090928, "dur":712, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026091641, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026092520, "dur":887, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026093407, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026094232, "dur":932, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026095164, "dur":718, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026095882, "dur":888, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026096770, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026097459, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026098076, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026098724, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026099365, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026100056, "dur":680, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026100736, "dur":685, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026086642, "dur":14779, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026101911, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026102493, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\monodoc.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026103038, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\cscompmgd.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026103574, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\WindowsBase.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026104155, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\WebMatrix.Data.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026104673, "dur":508, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SystemWebTestShim.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026106485, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026107467, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Workflow.Runtime.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026101421, "dur":8401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026109822, "dur":4812, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026114634, "dur":1738, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026116374, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026117105, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_vic3.info" }}
,{ "pid":12345, "tid":5, "ts":1754342026117170, "dur":801, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026117979, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026118683, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026119395, "dur":817, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026120218, "dur":881, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026121117, "dur":719, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026121854, "dur":776, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026122636, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026123340, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026123990, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026124689, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026125418, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026126105, "dur":249, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026126354, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026126489, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":5, "ts":1754342026126628, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026126874, "dur":506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":5, "ts":1754342026127380, "dur":445, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026141551, "dur":50902, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":5, "ts":1754342026192621, "dur":446, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026194375, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754342026194467, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026195973, "dur":527, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026197490, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026197645, "dur":166, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026197811, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.ContentLoadModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026197903, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026198136, "dur":545, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.HighDefinition.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754342026198682, "dur":751, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026200567, "dur":397, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026200964, "dur":51, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":5, "ts":1754342026201048, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026202732, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026204456, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026204589, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026204733, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026205001, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026205312, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026205839, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026206467, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026208155, "dur":417, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026209203, "dur":390, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026210702, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026210868, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.VisualEffectGraph.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754342026210918, "dur":1016, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026213066, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026214249, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754342026214300, "dur":299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026215787, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754342026217219, "dur":5990, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":5, "ts":1754342026223349, "dur":169470, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026078181, "dur":8463, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026086651, "dur":689, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026087341, "dur":728, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026088069, "dur":764, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026088833, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026089514, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026090343, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026090983, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026091697, "dur":787, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026092484, "dur":859, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026093344, "dur":815, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026094159, "dur":901, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026095060, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026095828, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026096706, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026097368, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026098020, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026098702, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026099336, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026100032, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026100682, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026086651, "dur":14698, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026101350, "dur":2450, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026103801, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026103801, "dur":6253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026110055, "dur":4853, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026114909, "dur":1437, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026116356, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026117024, "dur":469, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026117512, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026118178, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026118900, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026119611, "dur":991, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026120611, "dur":534, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026121177, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026121850, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026122600, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026123304, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026123939, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026124613, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026125369, "dur":794, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026126170, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":6, "ts":1754342026126350, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026126530, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":6, "ts":1754342026126675, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026126856, "dur":133, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":6, "ts":1754342026126990, "dur":216, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026127213, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":6, "ts":1754342026127377, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026127601, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":6, "ts":1754342026127775, "dur":208, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026127990, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026164069, "dur":27764, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":6, "ts":1754342026192007, "dur":227, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026193296, "dur":1099, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026195795, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026196136, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026196329, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026197855, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026198026, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026198204, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026198469, "dur":395, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026198902, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026199099, "dur":858, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026200792, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026202882, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026204616, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026204879, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026205228, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026205470, "dur":467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026205946, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026206559, "dur":503, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026208128, "dur":417, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026209173, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026210647, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754342026210748, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026212661, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026213795, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026214536, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026216615, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754342026218510, "dur":174314, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026078206, "dur":8446, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026086659, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026087373, "dur":722, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026088095, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026088862, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026089548, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026090345, "dur":660, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026091005, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026091688, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026092542, "dur":948, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026093491, "dur":899, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026094391, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026095231, "dur":749, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026095980, "dur":858, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026096838, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026097543, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026098174, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026098799, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026099432, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026100107, "dur":672, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026100779, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026086659, "dur":14856, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.dll" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":767, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Discovery.dll" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":507, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Activation.dll" }}
,{ "pid":12345, "tid":7, "ts":****************, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.Formatters.Soap.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026107449, "dur":500, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.DurableInstancing.dll" }}
,{ "pid":12345, "tid":7, "ts":1754342026101515, "dur":9287, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026110802, "dur":4484, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026115286, "dur":1004, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026116304, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026116960, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026117582, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026118206, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026118940, "dur":788, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026119740, "dur":949, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026120727, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026121295, "dur":667, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026121970, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026122681, "dur":666, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026123353, "dur":683, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026124042, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026124765, "dur":784, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026125556, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026126278, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":7, "ts":1754342026126518, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026126759, "dur":471, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":7, "ts":1754342026127231, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026127641, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":7, "ts":1754342026127880, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026153141, "dur":39869, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":7, "ts":1754342026193206, "dur":953, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026195569, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026195881, "dur":298, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026196216, "dur":610, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026197878, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026198066, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026198240, "dur":278, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026198557, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026199762, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026201297, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026203270, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026204961, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026205296, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026205711, "dur":584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026206305, "dur":344, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026206696, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026208509, "dur":452, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026209486, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026209629, "dur":292, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026209968, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026211805, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026213281, "dur":245, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026213563, "dur":386, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026213956, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026214763, "dur":851, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026231041, "dur":62, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754342026216708, "dur":14406, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":7, "ts":1754342026231258, "dur":161586, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026078232, "dur":8426, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026086663, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026087370, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026088077, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026088849, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026089588, "dur":778, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026090366, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026091081, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026091823, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026092549, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026093427, "dur":857, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026094284, "dur":903, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026095188, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026095924, "dur":875, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026096799, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026097479, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026098174, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026098832, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026099476, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026100133, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026100780, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026086663, "dur":14772, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026102046, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026102630, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.Razor.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026103155, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.Deployment.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Services.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":503, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.WebHost.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":522, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.SelfHost.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.Design.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":9213, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":4408, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026115057, "dur":1284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026116352, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026117079, "dur":76, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.IO.Compression.FileSystem-FeaturesChecked.txt_caef.info" }}
,{ "pid":12345, "tid":8, "ts":1754342026117162, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026117909, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026118611, "dur":756, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026119376, "dur":921, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026120328, "dur":907, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026121243, "dur":655, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026121906, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026122646, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026123379, "dur":622, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026124010, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026124718, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026125451, "dur":852, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026126307, "dur":705, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":8, "ts":1754342026127012, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026127461, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":8, "ts":1754342026127642, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026135310, "dur":61228, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":8, "ts":1754342026196691, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026198409, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026198906, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026199102, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026200640, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026200936, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026202754, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026204182, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026205669, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026206349, "dur":318, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026206718, "dur":495, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026208153, "dur":240, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026208402, "dur":359, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026208772, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026209008, "dur":286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026209335, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026210740, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754342026210807, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026212651, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026213802, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026214220, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026214773, "dur":647, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026216633, "dur":421, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754342026218299, "dur":34282, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":8, "ts":1754342026252721, "dur":140119, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026078260, "dur":8404, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026086672, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026087440, "dur":731, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026088171, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026088886, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026089698, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026090483, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026091278, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026092146, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026092937, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026093776, "dur":1019, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026094796, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026095565, "dur":870, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026096435, "dur":696, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026097131, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026097806, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026098500, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026099167, "dur":669, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026099836, "dur":684, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026100521, "dur":715, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026101236, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026086672, "dur":15133, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026101805, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CompilerServices.SymbolWriter.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026103349, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.C5.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026103941, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026104481, "dur":738, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Web.Infrastructure.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026106515, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Utilities.v4.0.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026107021, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026107544, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Framework.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026108544, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\ICSharpCode.SharpZipLib.dll" }}
,{ "pid":12345, "tid":9, "ts":1754342026101805, "dur":9231, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026111036, "dur":231, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026111267, "dur":5016, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026116307, "dur":767, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026117074, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_buxz.info" }}
,{ "pid":12345, "tid":9, "ts":1754342026117155, "dur":729, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026117893, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026118605, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026119313, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026120203, "dur":937, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026121175, "dur":649, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026121850, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026122556, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026123243, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026123927, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026124635, "dur":668, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026125311, "dur":773, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026126092, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026126232, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":9, "ts":1754342026126696, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026126906, "dur":500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":9, "ts":1754342026127406, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026151990, "dur":40559, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":9, "ts":1754342026192715, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026194833, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026195065, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.ServiceModel.Internals-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754342026195133, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026196639, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026198592, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VehiclesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754342026198766, "dur":661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026200330, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026200481, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026200681, "dur":458, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026202274, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026202445, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026203908, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026205461, "dur":482, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026205950, "dur":542, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026206508, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754342026206563, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026208042, "dur":386, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209085, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209370, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209495, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209626, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209829, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026209991, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026211680, "dur":658, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026213302, "dur":230, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026213571, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026213937, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026214542, "dur":708, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026216561, "dur":456, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754342026218295, "dur":14019, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1754342026232457, "dur":160365, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026078306, "dur":8372, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026086684, "dur":768, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026087452, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026088213, "dur":716, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026088929, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026089687, "dur":741, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026090428, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026091118, "dur":779, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026091898, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026092673, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026093566, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026094494, "dur":819, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026095314, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026096012, "dur":873, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026096885, "dur":651, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026097536, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026098195, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026098918, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026099573, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026100229, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026100877, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026086684, "dur":14858, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026102188, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026103259, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026103884, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026104435, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026108511, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.Microsoft.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026101542, "dur":9088, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026110630, "dur":4240, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026114870, "dur":1486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026116366, "dur":707, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026117086, "dur":682, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026117776, "dur":204, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026117987, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026118709, "dur":711, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026119426, "dur":979, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026120425, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026121070, "dur":629, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026121707, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026122455, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026123102, "dur":633, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026123742, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026124430, "dur":778, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026125215, "dur":781, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026126004, "dur":468, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026126476, "dur":559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":10, "ts":1754342026127036, "dur":320, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026127362, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":10, "ts":1754342026127536, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754342026128842, "dur":256418, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":10, "ts":1754342026385390, "dur":7464, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026078339, "dur":8363, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026086709, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026087434, "dur":725, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026088160, "dur":713, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026088873, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026089570, "dur":935, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026090505, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026091186, "dur":851, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026092037, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026092784, "dur":859, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026093643, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026094585, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026095346, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026096041, "dur":872, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026096913, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026097569, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026098252, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026098949, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026099572, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026100264, "dur":723, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026100987, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026086709, "dur":14976, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026101948, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\RabbitMQ.Client.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026102539, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026103063, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026103615, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.XBuild.Tasks.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026104662, "dur":501, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026108780, "dur":513, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Messaging.RabbitMQ.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026101685, "dur":9167, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026110853, "dur":2551, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026113404, "dur":2882, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026116304, "dur":661, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026116971, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026117583, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026118170, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026118828, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026119586, "dur":1141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026120734, "dur":652, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026121394, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026122095, "dur":721, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026122821, "dur":665, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026123493, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026124157, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026124863, "dur":766, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026125636, "dur":653, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026126293, "dur":605, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":11, "ts":1754342026126898, "dur":207, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026127112, "dur":415, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":11, "ts":1754342026127528, "dur":463, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026187261, "dur":82, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026184283, "dur":3106, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":11, "ts":1754342026187640, "dur":322, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026188296, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026188968, "dur":93, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":11, "ts":1754342026189069, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026189301, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026189432, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026189594, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026190110, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026191231, "dur":1965, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026195551, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026195982, "dur":267, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026196259, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026196811, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026198369, "dur":264, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026198675, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026200197, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026202068, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026203947, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026205555, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026206093, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026206629, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754342026206690, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026208286, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026208650, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026208889, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026209221, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026210888, "dur":868, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026212874, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026213003, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026213221, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026213603, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026214133, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026214771, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026216602, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestAssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754342026216678, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754342026218552, "dur":174268, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026078365, "dur":8344, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026086710, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026087482, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026088277, "dur":707, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026088984, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026089679, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026090436, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026091140, "dur":809, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026091950, "dur":667, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026092617, "dur":895, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026093512, "dur":880, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026094392, "dur":861, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026095253, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026095965, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026096858, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026097517, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026098180, "dur":693, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026098873, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026099516, "dur":717, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026100233, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026100953, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754342026086710, "dur":14943, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026101839, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Dynamic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026103423, "dur":629, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026104052, "dur":526, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.Protocols.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026104578, "dur":514, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Design.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026106330, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Services.Client.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026108666, "dur":542, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754342026101653, "dur":9011, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026110664, "dur":4588, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026115252, "dur":1044, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026116305, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026116984, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026117689, "dur":725, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026118422, "dur":721, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026119150, "dur":721, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026119878, "dur":898, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026120781, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026121329, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026122045, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026122780, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026123443, "dur":643, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026124091, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026124882, "dur":793, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026125681, "dur":648, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026126334, "dur":597, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":12, "ts":1754342026126932, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026127148, "dur":360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":12, "ts":1754342026127509, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026146731, "dur":45679, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":12, "ts":1754342026192570, "dur":921, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026194733, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026194977, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026195165, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026196681, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026198271, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026198521, "dur":162, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.DSPGraphModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754342026198684, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026200263, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026200423, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026200552, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026200919, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026202706, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026204353, "dur":626, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026206004, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026206584, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder.Stl-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754342026206640, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026208162, "dur":351, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026208523, "dur":283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026208813, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026209151, "dur":424, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026210649, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026210835, "dur":835, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026213086, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026213943, "dur":1015, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026215016, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754342026216878, "dur":13484, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":12, "ts":1754342026230520, "dur":162318, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754342026401688, "dur":2327, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 50798507, "ts": 1754342026407744, "dur": 2829, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 50798507, "ts": 1754342026411145, "dur": 1973, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 50798507, "ts": 1754342026404821, "dur": 8335, "ph": "X", "name": "Write chrome-trace events", "args": {} },
