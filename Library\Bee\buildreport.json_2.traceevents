{ "pid": 93864, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900712827, "dur": 46510, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900759339, "dur": 456525, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900759349, "dur": 25, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900759377, "dur": 772, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900760154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900760157, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900760179, "dur": 6, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900760186, "dur": 5235, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765431, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765435, "dur": 166, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765603, "dur": 2, "ph": "X", "name": "ProcessMessages 1416", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765607, "dur": 44, "ph": "X", "name": "ReadAsync 1416", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765655, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765657, "dur": 40, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765701, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765703, "dur": 34, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765740, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765743, "dur": 36, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765782, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765785, "dur": 32, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765820, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765822, "dur": 32, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765857, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765859, "dur": 32, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765894, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765896, "dur": 35, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765934, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765937, "dur": 33, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765973, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900765975, "dur": 26, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766004, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766006, "dur": 91, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766102, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766143, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766145, "dur": 35, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766183, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766186, "dur": 39, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766230, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766279, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766281, "dur": 31, "ph": "X", "name": "ReadAsync 680", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766316, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766318, "dur": 155, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766478, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766534, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766537, "dur": 32, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766571, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766573, "dur": 35, "ph": "X", "name": "ReadAsync 642", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766611, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766614, "dur": 34, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766652, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766654, "dur": 34, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766690, "dur": 13, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766705, "dur": 32, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766739, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766741, "dur": 29, "ph": "X", "name": "ReadAsync 506", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766773, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766775, "dur": 35, "ph": "X", "name": "ReadAsync 166", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766813, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766816, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766851, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766853, "dur": 29, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766884, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766887, "dur": 30, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766920, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766922, "dur": 32, "ph": "X", "name": "ReadAsync 473", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766957, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766960, "dur": 22, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766983, "dur": 2, "ph": "X", "name": "ProcessMessages 118", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900766986, "dur": 28, "ph": "X", "name": "ReadAsync 118", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767017, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767019, "dur": 35, "ph": "X", "name": "ReadAsync 183", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767058, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767060, "dur": 35, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767099, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767101, "dur": 48, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767151, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767153, "dur": 27, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767182, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767185, "dur": 30, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767217, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767219, "dur": 28, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767251, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767253, "dur": 22, "ph": "X", "name": "ReadAsync 237", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767276, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767278, "dur": 27, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767308, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767310, "dur": 23, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767334, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767336, "dur": 20, "ph": "X", "name": "ReadAsync 509", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767360, "dur": 24, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767387, "dur": 33, "ph": "X", "name": "ReadAsync 115", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767422, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767425, "dur": 28, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767456, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767458, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767488, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767490, "dur": 39, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767531, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767533, "dur": 30, "ph": "X", "name": "ReadAsync 465", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767565, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767568, "dur": 45, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767615, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767618, "dur": 34, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767654, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767656, "dur": 42, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767701, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767703, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767736, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767738, "dur": 29, "ph": "X", "name": "ReadAsync 684", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767770, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767772, "dur": 137, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767912, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767935, "dur": 28, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900767967, "dur": 31, "ph": "X", "name": "ReadAsync 98", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768014, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768016, "dur": 27, "ph": "X", "name": "ReadAsync 228", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768046, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768048, "dur": 315, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768368, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768446, "dur": 4, "ph": "X", "name": "ProcessMessages 4112", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768451, "dur": 23, "ph": "X", "name": "ReadAsync 4112", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768477, "dur": 23, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768503, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768506, "dur": 18, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768526, "dur": 9, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768538, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768565, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768567, "dur": 27, "ph": "X", "name": "ReadAsync 134", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768597, "dur": 1, "ph": "X", "name": "ProcessMessages 158", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768599, "dur": 18, "ph": "X", "name": "ReadAsync 158", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768620, "dur": 21, "ph": "X", "name": "ReadAsync 518", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768643, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768645, "dur": 33, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768681, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768683, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768701, "dur": 10, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768713, "dur": 11, "ph": "X", "name": "ReadAsync 221", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768727, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768756, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900768758, "dur": 289, "ph": "X", "name": "ReadAsync 134", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769050, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769053, "dur": 72, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769128, "dur": 4, "ph": "X", "name": "ProcessMessages 3833", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769134, "dur": 33, "ph": "X", "name": "ReadAsync 3833", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769171, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769173, "dur": 34, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769210, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769213, "dur": 35, "ph": "X", "name": "ReadAsync 560", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769250, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769252, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769272, "dur": 10, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769285, "dur": 10, "ph": "X", "name": "ReadAsync 163", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769297, "dur": 7, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769306, "dur": 12, "ph": "X", "name": "ReadAsync 101", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769320, "dur": 7, "ph": "X", "name": "ReadAsync 227", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769330, "dur": 8, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769340, "dur": 7, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769350, "dur": 11, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769362, "dur": 9, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769373, "dur": 10, "ph": "X", "name": "ReadAsync 71", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769385, "dur": 8, "ph": "X", "name": "ReadAsync 221", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769396, "dur": 26, "ph": "X", "name": "ReadAsync 77", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769425, "dur": 10, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769437, "dur": 12, "ph": "X", "name": "ReadAsync 205", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769451, "dur": 9, "ph": "X", "name": "ReadAsync 223", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769463, "dur": 9, "ph": "X", "name": "ReadAsync 141", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769474, "dur": 6, "ph": "X", "name": "ReadAsync 142", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769483, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769517, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769542, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769566, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769569, "dur": 24, "ph": "X", "name": "ReadAsync 63", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769595, "dur": 23, "ph": "X", "name": "ReadAsync 534", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769621, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769623, "dur": 31, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769656, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769658, "dur": 11, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769671, "dur": 12, "ph": "X", "name": "ReadAsync 163", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769685, "dur": 21, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769708, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769721, "dur": 31, "ph": "X", "name": "ReadAsync 197", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769756, "dur": 18, "ph": "X", "name": "ReadAsync 169", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769775, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769778, "dur": 100, "ph": "X", "name": "ReadAsync 626", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769890, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769893, "dur": 42, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769938, "dur": 1, "ph": "X", "name": "ProcessMessages 1190", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769940, "dur": 44, "ph": "X", "name": "ReadAsync 1190", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769987, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900769989, "dur": 61, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770053, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770056, "dur": 29, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770087, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770089, "dur": 44, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770136, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770138, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770160, "dur": 21, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770186, "dur": 34, "ph": "X", "name": "ReadAsync 159", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770222, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770225, "dur": 34, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770263, "dur": 23, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770290, "dur": 26, "ph": "X", "name": "ReadAsync 155", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770318, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770321, "dur": 29, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770352, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770354, "dur": 31, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770387, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770389, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770417, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770419, "dur": 63, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770485, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770487, "dur": 37, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770526, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770528, "dur": 30, "ph": "X", "name": "ReadAsync 860", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770560, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770562, "dur": 34, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770599, "dur": 13, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770614, "dur": 10, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770626, "dur": 8, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770636, "dur": 9, "ph": "X", "name": "ReadAsync 94", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770648, "dur": 22, "ph": "X", "name": "ReadAsync 130", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770674, "dur": 27, "ph": "X", "name": "ReadAsync 174", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770704, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770706, "dur": 28, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770736, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770738, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770753, "dur": 10, "ph": "X", "name": "ReadAsync 81", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770765, "dur": 13, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770782, "dur": 28, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770812, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770815, "dur": 17, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770835, "dur": 83, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770928, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770930, "dur": 63, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770995, "dur": 2, "ph": "X", "name": "ProcessMessages 1407", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900770998, "dur": 59, "ph": "X", "name": "ReadAsync 1407", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771061, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771089, "dur": 23, "ph": "X", "name": "ReadAsync 270", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771115, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771117, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771145, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771147, "dur": 25, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771174, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771176, "dur": 14, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771193, "dur": 14, "ph": "X", "name": "ReadAsync 217", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771209, "dur": 22, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771234, "dur": 42, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771279, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771281, "dur": 32, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771316, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771318, "dur": 17, "ph": "X", "name": "ReadAsync 554", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771338, "dur": 11, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771353, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771383, "dur": 26, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771413, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771415, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771451, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771452, "dur": 46, "ph": "X", "name": "ReadAsync 472", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771502, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771504, "dur": 48, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771555, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771557, "dur": 21, "ph": "X", "name": "ReadAsync 823", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771580, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771582, "dur": 13, "ph": "X", "name": "ReadAsync 526", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771597, "dur": 9, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771609, "dur": 12, "ph": "X", "name": "ReadAsync 33", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771623, "dur": 66, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771692, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771694, "dur": 33, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771729, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771731, "dur": 30, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771763, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771765, "dur": 32, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771800, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771802, "dur": 32, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771837, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771838, "dur": 28, "ph": "X", "name": "ReadAsync 615", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771868, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771870, "dur": 27, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771900, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771902, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771935, "dur": 26, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771966, "dur": 30, "ph": "X", "name": "ReadAsync 269", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771997, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900771999, "dur": 41, "ph": "X", "name": "ReadAsync 418", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772044, "dur": 30, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772077, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772079, "dur": 30, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772112, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772115, "dur": 34, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772152, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772154, "dur": 22, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772178, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772180, "dur": 28, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772210, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772212, "dur": 64, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772280, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772282, "dur": 35, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772320, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772322, "dur": 32, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772357, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772359, "dur": 32, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772393, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772395, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772421, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772423, "dur": 34, "ph": "X", "name": "ReadAsync 58", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772461, "dur": 32, "ph": "X", "name": "ReadAsync 236", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772496, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772498, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772517, "dur": 10, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772529, "dur": 24, "ph": "X", "name": "ReadAsync 285", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772555, "dur": 1, "ph": "X", "name": "ProcessMessages 87", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772557, "dur": 29, "ph": "X", "name": "ReadAsync 87", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772589, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772590, "dur": 55, "ph": "X", "name": "ReadAsync 556", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772650, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772674, "dur": 36, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772712, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772714, "dur": 28, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772745, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772746, "dur": 32, "ph": "X", "name": "ReadAsync 505", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772782, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772784, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772806, "dur": 15, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772824, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772839, "dur": 8, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772850, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772891, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772925, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772927, "dur": 34, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772964, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900772967, "dur": 34, "ph": "X", "name": "ReadAsync 521", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773004, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773007, "dur": 31, "ph": "X", "name": "ReadAsync 565", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773041, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773043, "dur": 31, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773077, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773079, "dur": 35, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773117, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773119, "dur": 27, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773149, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773150, "dur": 27, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773180, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773182, "dur": 30, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773216, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773218, "dur": 46, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773269, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773356, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773358, "dur": 28, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773390, "dur": 1, "ph": "X", "name": "ProcessMessages 185", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773392, "dur": 34, "ph": "X", "name": "ReadAsync 185", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773429, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773431, "dur": 28, "ph": "X", "name": "ReadAsync 685", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773461, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773463, "dur": 27, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773493, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773495, "dur": 30, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773528, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773530, "dur": 30, "ph": "X", "name": "ReadAsync 702", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773563, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773565, "dur": 33, "ph": "X", "name": "ReadAsync 442", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773601, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773604, "dur": 29, "ph": "X", "name": "ReadAsync 722", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773635, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773638, "dur": 46, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773687, "dur": 1, "ph": "X", "name": "ProcessMessages 777", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773690, "dur": 125, "ph": "X", "name": "ReadAsync 777", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773818, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773849, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900773947, "dur": 61, "ph": "X", "name": "ReadAsync 110", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774011, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774014, "dur": 80, "ph": "X", "name": "ReadAsync 586", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774098, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774101, "dur": 37, "ph": "X", "name": "ReadAsync 887", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774141, "dur": 1, "ph": "X", "name": "ProcessMessages 1099", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774143, "dur": 35, "ph": "X", "name": "ReadAsync 1099", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774180, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774182, "dur": 47, "ph": "X", "name": "ReadAsync 501", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774232, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774234, "dur": 40, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774277, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774279, "dur": 28, "ph": "X", "name": "ReadAsync 670", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774309, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774311, "dur": 24, "ph": "X", "name": "ReadAsync 421", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774339, "dur": 27, "ph": "X", "name": "ReadAsync 153", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774369, "dur": 40, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774412, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774413, "dur": 39, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774455, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774457, "dur": 68, "ph": "X", "name": "ReadAsync 492", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774527, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774529, "dur": 37, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774569, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774571, "dur": 44, "ph": "X", "name": "ReadAsync 941", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774618, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774620, "dur": 26, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774649, "dur": 23, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774676, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774696, "dur": 32, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774731, "dur": 23, "ph": "X", "name": "ReadAsync 143", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774757, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774759, "dur": 24, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774785, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774812, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774814, "dur": 28, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774846, "dur": 27, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774875, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774877, "dur": 28, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774907, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774910, "dur": 27, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774940, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774942, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900774971, "dur": 30, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775003, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775005, "dur": 26, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775034, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775036, "dur": 30, "ph": "X", "name": "ReadAsync 238", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775069, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775071, "dur": 30, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775104, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775106, "dur": 14, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775122, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775137, "dur": 9, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775148, "dur": 9, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775159, "dur": 10, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775171, "dur": 13, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775186, "dur": 12, "ph": "X", "name": "ReadAsync 138", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775201, "dur": 8, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775211, "dur": 11, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775224, "dur": 6, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775232, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775250, "dur": 7, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775259, "dur": 9, "ph": "X", "name": "ReadAsync 152", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775270, "dur": 7, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775280, "dur": 9, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775290, "dur": 6, "ph": "X", "name": "ReadAsync 162", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775299, "dur": 8, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775310, "dur": 7, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775319, "dur": 29, "ph": "X", "name": "ReadAsync 95", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775350, "dur": 7, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775359, "dur": 8, "ph": "X", "name": "ReadAsync 61", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775368, "dur": 7, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775377, "dur": 8, "ph": "X", "name": "ReadAsync 79", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775388, "dur": 9, "ph": "X", "name": "ReadAsync 153", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775399, "dur": 8, "ph": "X", "name": "ReadAsync 177", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775408, "dur": 8, "ph": "X", "name": "ReadAsync 142", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775418, "dur": 6, "ph": "X", "name": "ReadAsync 185", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775426, "dur": 8, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775435, "dur": 7, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775446, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775478, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775512, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775514, "dur": 27, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775544, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775546, "dur": 30, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775577, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775579, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775606, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775608, "dur": 42, "ph": "X", "name": "ReadAsync 423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775652, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775653, "dur": 27, "ph": "X", "name": "ReadAsync 658", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775683, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775685, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775710, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775712, "dur": 25, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775739, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775740, "dur": 24, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775767, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775769, "dur": 27, "ph": "X", "name": "ReadAsync 248", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775798, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775804, "dur": 58, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775866, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775868, "dur": 18, "ph": "X", "name": "ReadAsync 101", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775888, "dur": 37, "ph": "X", "name": "ReadAsync 566", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775927, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775929, "dur": 40, "ph": "X", "name": "ReadAsync 97", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775973, "dur": 24, "ph": "X", "name": "ReadAsync 276", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900775998, "dur": 6, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776006, "dur": 20, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776028, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776030, "dur": 19, "ph": "X", "name": "ReadAsync 97", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776051, "dur": 33, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776088, "dur": 26, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776116, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776118, "dur": 26, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776148, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776150, "dur": 25, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776177, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776179, "dur": 27, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776208, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776210, "dur": 26, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776238, "dur": 27, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776268, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776270, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776298, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776300, "dur": 26, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776329, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776331, "dur": 69, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776404, "dur": 38, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776446, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776448, "dur": 30, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776481, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776483, "dur": 35, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776521, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776522, "dur": 29, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776554, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776557, "dur": 33, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776591, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776594, "dur": 34, "ph": "X", "name": "ReadAsync 631", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776631, "dur": 30, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776663, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776665, "dur": 26, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776694, "dur": 28, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776724, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776726, "dur": 41, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776770, "dur": 63, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776835, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776837, "dur": 33, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776872, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776875, "dur": 64, "ph": "X", "name": "ReadAsync 939", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776941, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776943, "dur": 35, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776981, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900776983, "dur": 31, "ph": "X", "name": "ReadAsync 850", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777017, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777019, "dur": 10, "ph": "X", "name": "ReadAsync 415", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777032, "dur": 20, "ph": "X", "name": "ReadAsync 22", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777056, "dur": 16, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777074, "dur": 9, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777086, "dur": 24, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777113, "dur": 23, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777138, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777139, "dur": 20, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777163, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777188, "dur": 24, "ph": "X", "name": "ReadAsync 145", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777216, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777236, "dur": 31, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777270, "dur": 24, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777297, "dur": 23, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777323, "dur": 22, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777349, "dur": 23, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777375, "dur": 22, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777400, "dur": 61, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777465, "dur": 22, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777490, "dur": 28, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777521, "dur": 20, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777544, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777566, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777653, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777655, "dur": 33, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777690, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777692, "dur": 26, "ph": "X", "name": "ReadAsync 613", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777722, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777761, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777763, "dur": 29, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777796, "dur": 28, "ph": "X", "name": "ReadAsync 632", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777826, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777828, "dur": 30, "ph": "X", "name": "ReadAsync 275", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777862, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777894, "dur": 28, "ph": "X", "name": "ReadAsync 312", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777925, "dur": 28, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777957, "dur": 28, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900777988, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778017, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778020, "dur": 32, "ph": "X", "name": "ReadAsync 199", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778055, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778056, "dur": 38, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778096, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778098, "dur": 36, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778137, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778139, "dur": 45, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778186, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778188, "dur": 22, "ph": "X", "name": "ReadAsync 607", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778213, "dur": 10, "ph": "X", "name": "ReadAsync 418", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778225, "dur": 36, "ph": "X", "name": "ReadAsync 33", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778264, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778266, "dur": 32, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778301, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778304, "dur": 22, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778330, "dur": 37, "ph": "X", "name": "ReadAsync 655", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778369, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778371, "dur": 17, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778391, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778411, "dur": 20, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778434, "dur": 10, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778446, "dur": 9, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778457, "dur": 8, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778467, "dur": 7, "ph": "X", "name": "ReadAsync 15", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778476, "dur": 11, "ph": "X", "name": "ReadAsync 97", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778490, "dur": 9, "ph": "X", "name": "ReadAsync 297", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778501, "dur": 10, "ph": "X", "name": "ReadAsync 59", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778515, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778532, "dur": 24, "ph": "X", "name": "ReadAsync 254", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778559, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778561, "dur": 51, "ph": "X", "name": "ReadAsync 201", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778631, "dur": 36, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778668, "dur": 236, "ph": "X", "name": "ProcessMessages 1423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778906, "dur": 53, "ph": "X", "name": "ReadAsync 1423", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778961, "dur": 3, "ph": "X", "name": "ProcessMessages 3954", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778965, "dur": 28, "ph": "X", "name": "ReadAsync 3954", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778996, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900778998, "dur": 32, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779033, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779036, "dur": 38, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779077, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779079, "dur": 48, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779129, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779131, "dur": 57, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779191, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779193, "dur": 36, "ph": "X", "name": "ReadAsync 129", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779231, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779233, "dur": 22, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779259, "dur": 25, "ph": "X", "name": "ReadAsync 38", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779286, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779288, "dur": 29, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779319, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779321, "dur": 34, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779357, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779359, "dur": 28, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779390, "dur": 33, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779427, "dur": 30, "ph": "X", "name": "ReadAsync 184", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779460, "dur": 36, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779499, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779501, "dur": 29, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779533, "dur": 24, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779558, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779560, "dur": 25, "ph": "X", "name": "ReadAsync 467", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779589, "dur": 25, "ph": "X", "name": "ReadAsync 397", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779616, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779618, "dur": 27, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779648, "dur": 21, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779672, "dur": 20, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779695, "dur": 22, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779719, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779722, "dur": 23, "ph": "X", "name": "ReadAsync 168", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779747, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779767, "dur": 20, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779790, "dur": 20, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779813, "dur": 19, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779836, "dur": 22, "ph": "X", "name": "ReadAsync 174", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900779992, "dur": 36835, "ph": "X", "name": "ReadAsync 247", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900816837, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900816841, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900816876, "dur": 553, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817435, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817458, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817460, "dur": 193, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817659, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817685, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817687, "dur": 115, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817806, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817859, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817894, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817959, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900817999, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818001, "dur": 244, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818307, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818434, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818436, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818470, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818472, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818527, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818552, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818554, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818590, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818623, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818838, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818977, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818979, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900818999, "dur": 177, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819211, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819255, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819257, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819341, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819386, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819387, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819498, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819523, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819525, "dur": 125, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819655, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819669, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819709, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819786, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819825, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900819827, "dur": 175, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820007, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820032, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820095, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820184, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820213, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820280, "dur": 267, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820551, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820553, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820571, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820573, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820662, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820700, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820702, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820880, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820914, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820916, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820964, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820992, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900820994, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821030, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821057, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821135, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821165, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821167, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821182, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821219, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821249, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821268, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821290, "dur": 281, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821576, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821604, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821606, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821627, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821670, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821675, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821766, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821792, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821794, "dur": 62, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821861, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900821890, "dur": 250, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822145, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822174, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822234, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822267, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822385, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822473, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822475, "dur": 124, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822602, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822604, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900822623, "dur": 515, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823143, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823175, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823177, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823203, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823244, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823275, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823331, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823359, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823391, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823407, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823452, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823484, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823510, "dur": 196, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823728, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823786, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823788, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823827, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823854, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823857, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900823889, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824039, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824071, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824073, "dur": 46, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824122, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824124, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824361, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824402, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824433, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824460, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824482, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824527, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824556, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824594, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824692, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824694, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824734, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824754, "dur": 135, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824894, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824919, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900824973, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825012, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825041, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825083, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825108, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825110, "dur": 124, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825238, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825279, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825281, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825464, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825513, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825516, "dur": 107, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825630, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825659, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825661, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825696, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825697, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825789, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825816, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825855, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825896, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900825959, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826006, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826054, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826081, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826082, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826297, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826348, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826350, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826396, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826454, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826456, "dur": 126, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826587, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826617, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826619, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826702, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826736, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826738, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826771, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826773, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826821, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826838, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900826840, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827012, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827036, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827038, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827073, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827091, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827258, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827288, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827346, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827369, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827436, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827451, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827453, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827506, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827576, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827632, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827634, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827662, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827664, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827693, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827695, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827764, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827789, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827820, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827906, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827935, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900827937, "dur": 85, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828027, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828053, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828319, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828348, "dur": 201, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828554, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828646, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900828649, "dur": 1569, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830224, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830245, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830248, "dur": 315, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830568, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830611, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900830614, "dur": 829, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900831446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900831448, "dur": 248, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900831700, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900831703, "dur": 1266, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900832976, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833012, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833015, "dur": 432, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833452, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833471, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833473, "dur": 481, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833959, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833991, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900833994, "dur": 480, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900834479, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900834495, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900834497, "dur": 498, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900834999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835001, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835022, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835024, "dur": 662, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835692, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835728, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900835731, "dur": 525, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900836261, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900836286, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900836289, "dur": 16444, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900852743, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900852747, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900852783, "dur": 32231, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900885023, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900885027, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900885061, "dur": 32364, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917434, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917437, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917466, "dur": 297, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917773, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917854, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900917870, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918060, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918116, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918179, "dur": 33, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918216, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918347, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918348, "dur": 185, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918539, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918575, "dur": 312, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918893, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900918938, "dur": 354, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900919294, "dur": 21, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900919317, "dur": 385, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900919708, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900919776, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900919783, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920033, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920053, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920062, "dur": 51, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920118, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920161, "dur": 189, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920355, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920385, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920400, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920579, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920593, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920714, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920715, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920752, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920934, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900920949, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921109, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921127, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921171, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921199, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921201, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921229, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921254, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921368, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921405, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921451, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921484, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900921515, "dur": 544, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922063, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922065, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922098, "dur": 281, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922384, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922406, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922710, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922729, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900922987, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923022, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923024, "dur": 886, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923931, "dur": 16, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923951, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923988, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900923994, "dur": 3370, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900927376, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900927380, "dur": 247, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900927630, "dur": 18, "ph": "X", "name": "ProcessMessages 82", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900927651, "dur": 2471, "ph": "X", "name": "ReadAsync 82", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900930132, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900930136, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900930176, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900930178, "dur": 1139, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900931322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900931325, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900931360, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900931362, "dur": 1383, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932755, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932816, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932836, "dur": 15, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900932857, "dur": 403, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900933265, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900933284, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900933321, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900933341, "dur": 1061, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934407, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934451, "dur": 14, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934466, "dur": 412, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934883, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934921, "dur": 5, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900934928, "dur": 99, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935033, "dur": 68, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935106, "dur": 8, "ph": "X", "name": "ProcessMessages 38", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935116, "dur": 398, "ph": "X", "name": "ReadAsync 38", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935519, "dur": 128, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935653, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935684, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935801, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935821, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900935921, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936013, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936047, "dur": 268, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936318, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936320, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936354, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936356, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900936391, "dur": 728, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937126, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937165, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937168, "dur": 38, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937210, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937317, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937669, "dur": 190, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937863, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900937865, "dur": 2124, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900939995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900940000, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900940047, "dur": 15, "ph": "X", "name": "ProcessMessages 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900940064, "dur": 1123, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941197, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941200, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941226, "dur": 490, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941721, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941743, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900941744, "dur": 259, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900942018, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900942064, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900942066, "dur": 569, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900942641, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900942661, "dur": 841, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900943506, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900943508, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900943564, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900943570, "dur": 215, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900943792, "dur": 235, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944281, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944284, "dur": 135, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944426, "dur": 151, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944582, "dur": 326, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900944914, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945043, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945046, "dur": 169, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945220, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945256, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945258, "dur": 711, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945972, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900945988, "dur": 396, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946387, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946390, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946435, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946437, "dur": 306, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946750, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900946776, "dur": 575, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947357, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947383, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947385, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947671, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947673, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947740, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900947742, "dur": 444, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948192, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948253, "dur": 308, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948566, "dur": 133, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948702, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948704, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900948840, "dur": 533, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949379, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949416, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949419, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949460, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900949484, "dur": 1456, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900950949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900950952, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951002, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951005, "dur": 138, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951148, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951597, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951600, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951625, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951884, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951925, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951927, "dur": 29, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900951961, "dur": 223, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952189, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952214, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952245, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952247, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952275, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952302, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952551, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952578, "dur": 165, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952746, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952748, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952779, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952781, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952817, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952820, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952849, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900952870, "dur": 342, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953216, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953297, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953370, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953387, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953413, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953631, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953714, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953717, "dur": 109, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953831, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900953867, "dur": 953, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900954825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900954828, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900954854, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900954856, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900954983, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955014, "dur": 270, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955289, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955330, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955332, "dur": 20, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955359, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955429, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955476, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955544, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955558, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955588, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900955607, "dur": 948, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900956559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900956562, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900956665, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900956672, "dur": 384, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957069, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957096, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957098, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957128, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957145, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957219, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957290, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957314, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957604, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957622, "dur": 313, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957940, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957994, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900957996, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958034, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958036, "dur": 71, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958111, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958148, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958316, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958374, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958411, "dur": 237, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958653, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958707, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958709, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958866, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958870, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958905, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900958985, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959036, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959038, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959095, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959096, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959128, "dur": 454, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959587, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900959624, "dur": 439, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960069, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960101, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960353, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960419, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960421, "dur": 152, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960578, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960579, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960601, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960677, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960716, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960718, "dur": 92, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960815, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960846, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900960998, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961010, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961059, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961061, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961092, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961115, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961146, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961175, "dur": 173, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961353, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961398, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961436, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961488, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961519, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961521, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961687, "dur": 130, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961820, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961822, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961838, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900961840, "dur": 999, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900962843, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900962845, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900962887, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900962890, "dur": 846, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900963740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900963742, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900963775, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900963776, "dur": 41, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900963821, "dur": 534, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900964362, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900964558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900964561, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900964604, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900964606, "dur": 818, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965431, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965466, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965470, "dur": 60, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965533, "dur": 309, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965846, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900965848, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966005, "dur": 2, "ph": "X", "name": "ProcessMessages 104", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966010, "dur": 66, "ph": "X", "name": "ReadAsync 104", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966080, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966101, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966342, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966403, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900966406, "dur": 667, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900967077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900967079, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900967110, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900967112, "dur": 13416, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900980542, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900980547, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900980590, "dur": 20, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900980611, "dur": 2806, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900983428, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900983433, "dur": 482, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900983920, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900983940, "dur": 608, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900984554, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900984565, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900984644, "dur": 9, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900984655, "dur": 3849, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988514, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988519, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988569, "dur": 16, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988586, "dur": 179, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988771, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988794, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345900988817, "dur": 25189, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901014018, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901014024, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901014064, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901014080, "dur": 130335, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901144430, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901144435, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901144483, "dur": 21, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901144505, "dur": 21828, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901166342, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901166346, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901166381, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901166395, "dur": 28824, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901195230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901195234, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901195280, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901195287, "dur": 1413, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901196704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901196706, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901196757, "dur": 14, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901196772, "dur": 505, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901197283, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901197319, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 34359738368, "ts": 1754345901197321, "dur": 18533, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 52084545, "ts": 1754345901217355, "dur": 2891, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 30064771072, "ts": 1754345900700151, "dur": 515755, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 30064771072, "ts": 1754345900700278, "dur": 12477, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 30064771072, "ts": 1754345901215910, "dur": 8, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 30064771072, "ts": 1754345901215920, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 52084545, "ts": 1754345901220251, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754345900321032, "dur": 2977, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754345900324017, "dur": 373761, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754345900697781, "dur": 2340, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 52084545, "ts": 1754345901220261, "dur": 6, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754345900758977, "dur":3974, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345900762971, "dur":926, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345900763967, "dur":79, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1754345900764046, "dur":888, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345900766730, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/System.Security.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900767392, "dur":163, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900767560, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900767707, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900768024, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.AnimationModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900768709, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.MultiplayerModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900769402, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UmbraModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900769628, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.UnityWebRequestAudioModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900769840, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VFXModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1754345900770568, "dur":122, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.ComponentModel.Composition-FeaturesChecked.txt_91q2.info" }}
,{ "pid":12345, "tid":0, "ts":1754345900771246, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Numerics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900771619, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900771858, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Burst-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900772200, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Mathematics-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900772914, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900773436, "dur":149, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.RenderPipelines.HighDefinition.Runtime-FeaturesChecked.txt_v1r7.info" }}
,{ "pid":12345, "tid":0, "ts":1754345900775959, "dur":74, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MultiplayerModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900776626, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754345900777042, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_5hsl.info" }}
,{ "pid":12345, "tid":0, "ts":1754345900777200, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_uyte.info" }}
,{ "pid":12345, "tid":0, "ts":1754345900778732, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info" }}
,{ "pid":12345, "tid":0, "ts":1754345900778971, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":0, "ts":1754345900764988, "dur":14501, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345900779497, "dur":416895, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345901196393, "dur":250, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345901196902, "dur":3927, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754345900764979, "dur":14527, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900786355, "dur":548, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754345900786903, "dur":2361, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754345900789265, "dur":20135, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754345900809400, "dur":296, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754345900809696, "dur":4850, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754345900814548, "dur":331, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754345900814879, "dur":469, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754345900815348, "dur":502, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754345900815850, "dur":146, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754345900815996, "dur":92, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754345900779517, "dur":36592, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900816120, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754345900816189, "dur":217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900817072, "dur":820, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900817898, "dur":427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900818328, "dur":213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900818567, "dur":220, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900818789, "dur":287, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900819078, "dur":535, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900819615, "dur":633, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900820250, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900820444, "dur":933, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900821378, "dur":281, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900821661, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900821840, "dur":599, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900822441, "dur":354, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900822798, "dur":244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900823044, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900823252, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900823478, "dur":188, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900823668, "dur":381, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900824051, "dur":921, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900824975, "dur":357, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900825334, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900825495, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900825689, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900826059, "dur":551, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900826612, "dur":2252, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900828867, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900829023, "dur":164, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900829192, "dur":744, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900829938, "dur":410, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900830350, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900830568, "dur":699, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900831268, "dur":486, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900831756, "dur":360, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900832118, "dur":309, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900833133, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754345900833312, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754345900833485, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754345900833661, "dur":357, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754345900816426, "dur":18751, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754345900835178, "dur":17131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900867612, "dur":16608, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754345900884221, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900885890, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900886008, "dur":30903, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900916913, "dur":62, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754345900916982, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900917282, "dur":201, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900917492, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900917652, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900917825, "dur":288, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900918395, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900918909, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900919661, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.VFXModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900920134, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900920848, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900921040, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900921990, "dur":332, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900922627, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900923764, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.RenderPipelines.Core.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900923824, "dur":3280, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900930929, "dur":1700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900932630, "dur":221, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900934915, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900935631, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900936255, "dur":4990, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900942834, "dur":186, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900943030, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900943167, "dur":222, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900943398, "dur":1024, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900944476, "dur":1251, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900947639, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ClothModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900947767, "dur":460, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900950672, "dur":1180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900951860, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900952536, "dur":909, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900954786, "dur":419, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900955241, "dur":805, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900957194, "dur":406, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900957639, "dur":838, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900959573, "dur":78, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AccessibilityModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900959664, "dur":513, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900961154, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900963203, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.EnterpriseServices-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754345900963259, "dur":845, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754345900965413, "dur":22996, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":1, "ts":1754345900988555, "dur":207818, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900764997, "dur":14522, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900780097, "dur":1118, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\WindowsBase.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900781216, "dur":890, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754345900782106, "dur":674, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":2, "ts":1754345900782780, "dur":762, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754345900783542, "dur":804, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900784346, "dur":683, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":2, "ts":1754345900785030, "dur":803, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900785834, "dur":815, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900786649, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754345900787305, "dur":878, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900788183, "dur":1026, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900789210, "dur":972, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900790182, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900791013, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900791775, "dur":803, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900792579, "dur":1036, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900793615, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900779530, "dur":14860, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900794391, "dur":939, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":2, "ts":1754345900795331, "dur":821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":2, "ts":1754345900796168, "dur":691, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":2, "ts":1754345900797760, "dur":664, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\createdump.exe" }}
,{ "pid":12345, "tid":2, "ts":1754345900798555, "dur":1028, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\clrjit.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900800822, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.VisionOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900801446, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.UWP.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900794391, "dur":7846, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900805467, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900807282, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900807859, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900802238, "dur":6241, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900809809, "dur":827, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.GIModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900810636, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.DirectorModule.dll" }}
,{ "pid":12345, "tid":2, "ts":1754345900808479, "dur":5952, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900814431, "dur":1784, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900816217, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900817321, "dur":1117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900818446, "dur":668, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900819120, "dur":774, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900819902, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900820514, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900821219, "dur":714, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900821954, "dur":1117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900823086, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900823647, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900824221, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900824885, "dur":792, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900825692, "dur":170, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900825867, "dur":417, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":2, "ts":1754345900826285, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900826398, "dur":485, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":2, "ts":1754345900826884, "dur":334, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900828630, "dur":89805, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":2, "ts":1754345900918703, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900919113, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AMDModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754345900919168, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900919891, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900920050, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900920490, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900920640, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900920795, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.IO.Compression.FileSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754345900920858, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900925579, "dur":1248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900930890, "dur":1583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900934466, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900935910, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900936295, "dur":1960, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900941133, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754345900941283, "dur":816, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900946057, "dur":1192, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900948446, "dur":1131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900950414, "dur":1135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900951558, "dur":720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900952318, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900954181, "dur":684, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900956517, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900957005, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900957130, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900957250, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900957733, "dur":971, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900959731, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900960308, "dur":472, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900960787, "dur":319, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900961114, "dur":962, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900962114, "dur":1263, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900964897, "dur":1033, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754345900966809, "dur":229575, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900765329, "dur":14277, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900779613, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900780284, "dur":1110, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900781395, "dur":821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900782216, "dur":675, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900782892, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900783641, "dur":926, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900784567, "dur":991, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900785558, "dur":940, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900786499, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900787148, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900788066, "dur":1148, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900789214, "dur":986, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900790201, "dur":877, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900791078, "dur":819, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900791898, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900792826, "dur":925, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900793751, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900794528, "dur":1015, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900795543, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900796187, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900779613, "dur":17196, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900797265, "dur":1312, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Linq.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900798577, "dur":1243, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Interfaces.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900799820, "dur":655, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Experimental.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900800475, "dur":678, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Debugger.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900801153, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Core.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900801764, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900802821, "dur":518, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900803619, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.WebRequest.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900804153, "dur":913, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.Formatting.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900805758, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Json.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900807076, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IdentityModel.Selectors.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900807880, "dur":677, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":3, "ts":1754345900796810, "dur":11747, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900808557, "dur":3383, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900811941, "dur":4201, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900816150, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900816766, "dur":1080, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900817856, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900818505, "dur":750, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900819263, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900819991, "dur":659, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900820658, "dur":717, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900821393, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900822099, "dur":929, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900823035, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900823657, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900824354, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900825068, "dur":940, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900826014, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":3, "ts":1754345900826340, "dur":358, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900826705, "dur":711, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":3, "ts":1754345900827417, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900827539, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900934567, "dur":67, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900835133, "dur":99510, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":3, "ts":1754345900934799, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900935517, "dur":870, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900936422, "dur":1584, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900938830, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900939085, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900939201, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900939394, "dur":2141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900946174, "dur":1175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900949186, "dur":516, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900950399, "dur":1000, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900951406, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900952185, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900953714, "dur":718, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900955416, "dur":1221, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900957698, "dur":874, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900959675, "dur":611, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900960334, "dur":381, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900960725, "dur":229, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900960963, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900961422, "dur":1361, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900964251, "dur":944, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754345900966737, "dur":229650, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900765678, "dur":14003, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900779682, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900780513, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900781431, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900782284, "dur":700, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900782985, "dur":811, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900783796, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900784618, "dur":846, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900785464, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900786120, "dur":776, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900786897, "dur":766, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900787663, "dur":1161, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900788824, "dur":799, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900789623, "dur":946, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900790569, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900791308, "dur":800, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900792109, "dur":1065, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900793174, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900794028, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900794691, "dur":985, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900795677, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":4, "ts":1754345900779682, "dur":16706, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900797541, "dur":989, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\cscompmgd.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900798530, "dur":1039, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\WindowsBase.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900800016, "dur":584, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SystemWebTestShim.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900800600, "dur":506, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900801917, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900803688, "dur":504, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900796388, "dur":8236, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900804624, "dur":5717, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900810341, "dur":254, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900810596, "dur":5581, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900816179, "dur":633, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900816824, "dur":742, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900817574, "dur":753, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900818336, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900818975, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900819685, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900820279, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900820908, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900821489, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900822206, "dur":795, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900823007, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900823605, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900824155, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900824733, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900825319, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900826025, "dur":438, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":4, "ts":1754345900826464, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900826663, "dur":443, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":4, "ts":1754345900827106, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900830080, "dur":103030, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":4, "ts":1754345900933278, "dur":1299, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900934577, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SpriteMaskModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1754345900935231, "dur":702, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900935978, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900937638, "dur":175, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900937820, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900937960, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900938129, "dur":488, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900938950, "dur":2217, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900942057, "dur":997, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900943563, "dur":839, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900946027, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900946149, "dur":112, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll" }}
,{ "pid":12345, "tid":4, "ts":1754345900946297, "dur":970, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900948206, "dur":791, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900950681, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900951298, "dur":813, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900952121, "dur":287, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900952444, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900954323, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900954481, "dur":285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900954777, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900955093, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900955239, "dur":572, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900955847, "dur":901, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900957515, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900959170, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900960588, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900960769, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900961094, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900963207, "dur":761, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754345900965270, "dur":14839, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":4, "ts":1754345900980342, "dur":216026, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900765528, "dur":14086, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900779624, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900780287, "dur":1012, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900781300, "dur":851, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900782152, "dur":693, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900782845, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900783572, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900784364, "dur":718, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900785082, "dur":860, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900785942, "dur":821, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900786763, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900787416, "dur":1136, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900788553, "dur":890, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900789443, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900790409, "dur":827, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900791236, "dur":783, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900792019, "dur":795, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900792815, "dur":909, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900793724, "dur":730, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900794454, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900795396, "dur":740, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900779624, "dur":16512, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900796137, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Analytics.deps.json" }}
,{ "pid":12345, "tid":5, "ts":1754345900799265, "dur":5763, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900796137, "dur":9067, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900805205, "dur":5936, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900811141, "dur":5056, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900816209, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900816888, "dur":1016, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900817910, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900818074, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900818627, "dur":636, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900819268, "dur":713, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900819987, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900820585, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900821254, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900821863, "dur":882, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900822752, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900823454, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900824034, "dur":480, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900824522, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900825113, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900825863, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900826157, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900826300, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":5, "ts":1754345900826473, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900826637, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":5, "ts":1754345900826775, "dur":202, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900826982, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":5, "ts":1754345900827137, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900830950, "dur":96023, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":5, "ts":1754345900927144, "dur":2532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900932477, "dur":302, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900932780, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1754345900934863, "dur":551, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900935424, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900936220, "dur":1283, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900938569, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900938773, "dur":274, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900939084, "dur":2081, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900942061, "dur":985, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900943486, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900944319, "dur":498, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900944851, "dur":824, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900946941, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900948846, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900950315, "dur":967, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900951301, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900952016, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900952468, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900954330, "dur":490, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900955489, "dur":1090, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900957237, "dur":366, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900957615, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900958040, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900959655, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900960907, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900961295, "dur":786, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900962121, "dur":1019, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900964571, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754345900965919, "dur":17915, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":5, "ts":1754345900983958, "dur":212413, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900765087, "dur":14444, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900779540, "dur":1039, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900780580, "dur":910, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900781490, "dur":857, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900782348, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900783043, "dur":932, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900783975, "dur":786, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900784761, "dur":1094, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900785856, "dur":1144, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900787000, "dur":913, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900787914, "dur":1138, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900789053, "dur":1044, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900790097, "dur":885, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":6, "ts":1754345900790982, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900791628, "dur":1141, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900792770, "dur":893, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900793663, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900794492, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.XDocument.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900795459, "dur":756, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900796216, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900779540, "dur":17573, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900797113, "dur":633, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\SMDiagnostics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900797746, "dur":1413, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\RabbitMQ.Client.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900799159, "dur":892, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\PEAPI.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900800052, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Novell.Directory.Ldap.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900800711, "dur":680, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.XBuild.Tasks.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900801850, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Tasklets.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900803358, "dur":834, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.Win32.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900804629, "dur":860, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Posix.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900805490, "dur":681, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Parallel.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900797113, "dur":10809, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900809475, "dur":956, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.WindModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900810432, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VirtualTexturingModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900811137, "dur":665, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":6, "ts":1754345900807922, "dur":5828, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900813750, "dur":2507, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900816260, "dur":948, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900817222, "dur":838, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900818066, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900818741, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900819418, "dur":752, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900820176, "dur":631, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900820812, "dur":687, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900821505, "dur":1157, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900822672, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900823356, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900823990, "dur":608, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900824606, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900825256, "dur":748, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900826011, "dur":286, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":6, "ts":1754345900826297, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900826644, "dur":259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":6, "ts":1754345900826903, "dur":199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900827107, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":6, "ts":1754345900827238, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900833074, "dur":105463, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":6, "ts":1754345900938712, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900938871, "dur":2566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900943111, "dur":930, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900944083, "dur":1389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900947168, "dur":673, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900949298, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityCurlModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754345900949379, "dur":1355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900951368, "dur":741, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900952117, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900952442, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ARModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754345900952502, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900954583, "dur":431, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900957118, "dur":439, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900957568, "dur":407, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900958016, "dur":691, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900959898, "dur":512, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900961353, "dur":1000, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900964182, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProGrids-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1754345900964240, "dur":913, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900987574, "dur":80, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754345900965916, "dur":21751, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":6, "ts":1754345900987806, "dur":208573, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900765113, "dur":14426, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900779547, "dur":1089, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900780636, "dur":918, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900781554, "dur":757, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900782311, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900782969, "dur":838, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900783807, "dur":869, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900784676, "dur":1054, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900785730, "dur":837, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900786567, "dur":825, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900787392, "dur":1344, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900788737, "dur":796, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900789534, "dur":996, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900790531, "dur":843, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900791374, "dur":1144, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900792519, "dur":992, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900793511, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900794287, "dur":838, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900795125, "dur":923, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900796049, "dur":649, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900779547, "dur":17475, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900797193, "dur":929, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Dynamic.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900798122, "dur":1512, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900799635, "dur":889, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.Design.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900800524, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900801062, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.DirectoryServices.Protocols.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900802040, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Deployment.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900803693, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.OracleClient.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900804546, "dur":706, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900805253, "dur":662, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.Entity.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900806740, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Configuration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900797022, "dur":11257, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900809882, "dur":1042, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.UIModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754345900808279, "dur":6038, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900814317, "dur":1891, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900816213, "dur":1024, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900817248, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900818159, "dur":690, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900818860, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900819611, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900820204, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900820773, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900821396, "dur":800, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900822202, "dur":931, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900823139, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900823730, "dur":632, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900824369, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900825012, "dur":864, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900825881, "dur":293, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":7, "ts":1754345900826175, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900826449, "dur":531, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":7, "ts":1754345900826980, "dur":269, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754345900829555, "dur":336264, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":7, "ts":1754345901165953, "dur":30436, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900765148, "dur":14400, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900779556, "dur":673, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900780229, "dur":1103, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900781332, "dur":863, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900782195, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900782839, "dur":698, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900783537, "dur":856, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900784393, "dur":914, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900785307, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900786146, "dur":789, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900786935, "dur":794, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900787730, "dur":1118, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900788848, "dur":1018, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900789867, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900790736, "dur":701, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900791438, "dur":774, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900792212, "dur":1055, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.OpenSsl.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900793268, "dur":837, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900794105, "dur":742, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900794847, "dur":1013, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900795860, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900779556, "dur":16957, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900797463, "dur":1446, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.WebPages.Deployment.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":1048, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Services.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":612, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Routing.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.RegularExpressions.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Razor.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Mvc.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":785, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Http.SelfHost.dll" }}
,{ "pid":12345, "tid":8, "ts":****************, "dur":833, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900805296, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.Extensions.Design.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900796513, "dur":10906, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900809687, "dur":652, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900807419, "dur":3889, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900811308, "dur":4885, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900816198, "dur":1199, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900817406, "dur":901, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900818315, "dur":768, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900819091, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900819804, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900820446, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt_5hsl.info" }}
,{ "pid":12345, "tid":8, "ts":1754345900820513, "dur":680, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900821201, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900821770, "dur":973, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900822749, "dur":606, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900823360, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900824093, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900824684, "dur":789, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900825481, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900826184, "dur":375, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":8, "ts":1754345900826559, "dur":355, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900826919, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":8, "ts":1754345900827052, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900827202, "dur":227, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":8, "ts":1754345900827429, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900834630, "dur":98639, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":8, "ts":1754345900933397, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900933548, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900933690, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900933820, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900934015, "dur":692, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900935824, "dur":1006, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900936894, "dur":1484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900941291, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900945439, "dur":441, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900947351, "dur":749, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900949489, "dur":600, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900950481, "dur":855, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900951344, "dur":921, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900952266, "dur":114, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":8, "ts":1754345900952417, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900954389, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900954592, "dur":484, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900955083, "dur":114, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900955204, "dur":698, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900955943, "dur":893, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900957549, "dur":755, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900959390, "dur":575, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900960904, "dur":383, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900961298, "dur":753, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900962056, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Formats.Fbx.Runtime-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754345900962207, "dur":1193, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900981668, "dur":61, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754345900965007, "dur":16737, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":8, "ts":1754345900981918, "dur":214451, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900765197, "dur":14360, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900779563, "dur":908, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900780471, "dur":985, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900781456, "dur":818, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900782274, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900782931, "dur":728, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900783659, "dur":929, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900784589, "dur":984, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900785573, "dur":802, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900786375, "dur":679, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900787054, "dur":858, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900787913, "dur":1110, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900789023, "dur":964, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900789987, "dur":853, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900790841, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900791495, "dur":782, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900792278, "dur":1141, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900793419, "dur":781, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900794200, "dur":782, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900794982, "dur":927, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900795909, "dur":682, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900779563, "dur":17028, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":873, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":1780, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Routing.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Discovery.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Activation.dll" }}
,{ "pid":12345, "tid":9, "ts":****************, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":9, "ts":**************16, "dur":598, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Caching.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900804415, "dur":774, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reflection.Context.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900805189, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Windows.Threading.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900796592, "dur":10623, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900807569, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900808162, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Registry.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900808819, "dur":502, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900807215, "dur":5184, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900812400, "dur":3729, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900816138, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900816855, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900817478, "dur":909, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900818392, "dur":757, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900819154, "dur":788, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900819951, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900820512, "dur":754, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900821271, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900821883, "dur":942, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900822830, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900823474, "dur":617, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900824099, "dur":605, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900824710, "dur":701, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900825419, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900826132, "dur":392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":9, "ts":1754345900826527, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900826883, "dur":866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1754345900827749, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900835885, "dur":98630, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":9, "ts":1754345900934657, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900935839, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900936941, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900937125, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900937252, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900937480, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900937610, "dur":578, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900938196, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900938461, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900938607, "dur":428, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900939074, "dur":2454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900943357, "dur":1367, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900944779, "dur":1356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900946135, "dur":125, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754345900948173, "dur":902, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900950182, "dur":1135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900951326, "dur":881, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900952208, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/netstandard.dll" }}
,{ "pid":12345, "tid":9, "ts":1754345900952287, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754345900952339, "dur":642, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900954257, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900956660, "dur":420, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900957088, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900957633, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections.LowLevel.ILSupport-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754345900957692, "dur":892, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900959684, "dur":595, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900960309, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900960740, "dur":393, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900961140, "dur":820, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900962021, "dur":1095, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900964629, "dur":1067, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900985703, "dur":55, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754345900966723, "dur":19053, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":9, "ts":1754345900986010, "dur":210383, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900765572, "dur":14051, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900779634, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900780261, "dur":1028, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900781289, "dur":892, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900782182, "dur":673, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900782856, "dur":754, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900783610, "dur":765, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900784375, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900785102, "dur":849, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900785952, "dur":797, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900786750, "dur":656, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900787406, "dur":1023, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900788429, "dur":883, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900789312, "dur":957, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900790269, "dur":835, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900791104, "dur":739, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900791843, "dur":748, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900792592, "dur":982, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900793574, "dur":786, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900794360, "dur":856, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900795216, "dur":792, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900779634, "dur":16374, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900796009, "dur":670, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900798880, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Telemetry.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900800043, "dur":5246, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.BinLog.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900805289, "dur":621, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.BeeDriver2.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900796009, "dur":10713, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900806722, "dur":5131, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900811854, "dur":4271, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900816139, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900816770, "dur":672, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900817455, "dur":758, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900818222, "dur":737, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900818967, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900819683, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900820272, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900820842, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900821413, "dur":582, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900822002, "dur":864, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900822875, "dur":609, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900823491, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900824076, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900824646, "dur":598, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900825252, "dur":764, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900826021, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":10, "ts":1754345900826198, "dur":173, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900826376, "dur":444, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":10, "ts":1754345900826821, "dur":249, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900827074, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1754345900827267, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900827427, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900917937, "dur":1770, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":10, "ts":1754345900919848, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900920248, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900920369, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900920564, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900920698, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900920891, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900921836, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900922436, "dur":165, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900922903, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900926275, "dur":650, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900929725, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754345900929777, "dur":1146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900932804, "dur":1123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900934863, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900935457, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900935855, "dur":368, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900936264, "dur":1388, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900938775, "dur":265, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900939082, "dur":2090, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900942090, "dur":962, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900943550, "dur":747, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900946036, "dur":936, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900948197, "dur":760, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900949912, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900950043, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900950180, "dur":1073, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900951262, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900951913, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900952470, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900954343, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900954526, "dur":408, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900954941, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900955137, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900955786, "dur":908, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900957303, "dur":258, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900958694, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900960087, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900961269, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900962032, "dur":1051, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900964532, "dur":656, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754345900965913, "dur":14207, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":10, "ts":1754345900980280, "dur":216087, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900765602, "dur":14063, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900779680, "dur":986, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900780666, "dur":808, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900781474, "dur":758, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900782232, "dur":704, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900782937, "dur":829, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900783767, "dur":763, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900784530, "dur":913, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900785443, "dur":727, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900786170, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900786962, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900787753, "dur":1167, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900788921, "dur":747, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900789668, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900790610, "dur":705, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900791315, "dur":805, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900792120, "dur":966, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900793086, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900793861, "dur":719, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900794580, "dur":1004, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900795585, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900779680, "dur":16551, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900796231, "dur":2562, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900798794, "dur":850, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.CJK.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900798793, "dur":3385, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900802179, "dur":3820, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900805999, "dur":5652, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900811652, "dur":4459, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900816120, "dur":52, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":11, "ts":1754345900816173, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900816777, "dur":232, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754345900817025, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":11, "ts":1754345900817354, "dur":585, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900817941, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900818149, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900818324, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900818557, "dur":201, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900818763, "dur":301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900819070, "dur":497, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900819569, "dur":665, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900820236, "dur":194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900820439, "dur":923, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900821365, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900821610, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900821809, "dur":595, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900822406, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900822614, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900822794, "dur":202, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900823008, "dur":201, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900823215, "dur":185, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900823405, "dur":165, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900823576, "dur":423, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900824001, "dur":968, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900824972, "dur":238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900825216, "dur":228, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900825451, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900825674, "dur":379, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900826056, "dur":322, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900826380, "dur":2389, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900828772, "dur":191, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900828970, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900829149, "dur":762, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900829913, "dur":346, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900830266, "dur":251, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900830523, "dur":701, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900831226, "dur":502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900831731, "dur":248, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900831981, "dur":407, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":11, "ts":1754345900816763, "dur":16405, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754345900833967, "dur":124, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754345900835099, "dur":308813, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754345901194631, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754345901194510, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":11, "ts":1754345901194809, "dur":1522, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":12, "ts":1754345900765268, "dur":14296, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900779576, "dur":1123, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900780699, "dur":816, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900781516, "dur":801, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900782317, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900782981, "dur":976, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900783957, "dur":1095, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900785052, "dur":868, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900785920, "dur":928, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900786849, "dur":761, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900787610, "dur":1120, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900788730, "dur":857, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900789588, "dur":1055, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900790643, "dur":746, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900791389, "dur":784, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900792174, "dur":1204, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900793379, "dur":827, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900794206, "dur":822, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900795028, "dur":976, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900796004, "dur":810, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900779576, "dur":17616, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900797193, "dur":544, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CompilerServices.SymbolWriter.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900797737, "dur":1324, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Cairo.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900799061, "dur":961, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900800022, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.C5.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900800716, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Btls.Interface.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900801317, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Web.Infrastructure.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900801844, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.VisualC.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900803694, "dur":898, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Tasks.v4.0.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900804592, "dur":838, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Microsoft.Build.Framework.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900806514, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900807065, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.West.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900797193, "dur":11218, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900809892, "dur":786, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900811142, "dur":736, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.ParticleSystemModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900808412, "dur":6110, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900814523, "dur":1603, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900816137, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900816761, "dur":1150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900817917, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900818558, "dur":836, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900819402, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900820124, "dur":613, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900820771, "dur":604, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900821389, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900822059, "dur":889, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900822955, "dur":645, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900823635, "dur":657, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900824298, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900824882, "dur":695, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900825589, "dur":745, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900826339, "dur":453, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":12, "ts":1754345900826793, "dur":234, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900827035, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754345900827233, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900832591, "dur":99607, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":12, "ts":1754345900932379, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900932834, "dur":124, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GameCenterModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754345900934942, "dur":660, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900935611, "dur":602, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900936252, "dur":4547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900943112, "dur":310, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900943430, "dur":1789, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900945275, "dur":663, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900947659, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754345900947715, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900949723, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900949866, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900949975, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900950127, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900950241, "dur":1544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900951797, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900952422, "dur":594, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900954503, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900954985, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900955153, "dur":954, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900956144, "dur":748, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900957576, "dur":677, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900959377, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754345900959433, "dur":531, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900960920, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900961306, "dur":704, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900962050, "dur":1343, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900964959, "dur":446, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754345900965922, "dur":47547, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":12, "ts":1754345901013616, "dur":182758, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754345901208634, "dur":6123, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 52084545, "ts": 1754345901220300, "dur": 4381, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 52084545, "ts": 1754345901224823, "dur": 4795, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 52084545, "ts": 1754345901217352, "dur": 12316, "ph": "X", "name": "Write chrome-trace events", "args": {} },
