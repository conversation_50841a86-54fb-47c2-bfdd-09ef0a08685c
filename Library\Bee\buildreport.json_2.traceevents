{ "pid": 93864, "tid": 81604378624, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328450072, "dur": 32155, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482229, "dur": 339934, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482242, "dur": 63, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482309, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482311, "dur": 467, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482782, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482812, "dur": 3, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328482817, "dur": 2978, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485799, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485837, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485839, "dur": 24, "ph": "X", "name": "ReadAsync 304", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485866, "dur": 28, "ph": "X", "name": "ReadAsync 120", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485897, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485924, "dur": 20, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485946, "dur": 37, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485987, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328485990, "dur": 34, "ph": "X", "name": "ReadAsync 261", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486026, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486029, "dur": 33, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486064, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486065, "dur": 30, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486098, "dur": 26, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486128, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486156, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486158, "dur": 28, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486188, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486191, "dur": 36, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486229, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486231, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486254, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486276, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486278, "dur": 56, "ph": "X", "name": "ReadAsync 234", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486338, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486457, "dur": 34, "ph": "X", "name": "ReadAsync 207", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486493, "dur": 1, "ph": "X", "name": "ProcessMessages 1420", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486495, "dur": 53, "ph": "X", "name": "ReadAsync 1420", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486552, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486582, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486584, "dur": 20, "ph": "X", "name": "ReadAsync 709", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486606, "dur": 23, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486631, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486633, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486660, "dur": 23, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486684, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486685, "dur": 22, "ph": "X", "name": "ReadAsync 403", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486710, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486730, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486755, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486777, "dur": 20, "ph": "X", "name": "ReadAsync 399", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486800, "dur": 21, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486824, "dur": 23, "ph": "X", "name": "ReadAsync 542", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486850, "dur": 20, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486872, "dur": 19, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486894, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486915, "dur": 21, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486938, "dur": 19, "ph": "X", "name": "ReadAsync 528", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486961, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328486986, "dur": 23, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487011, "dur": 29, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487043, "dur": 25, "ph": "X", "name": "ReadAsync 493", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487073, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487096, "dur": 21, "ph": "X", "name": "ReadAsync 451", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487120, "dur": 28, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487150, "dur": 21, "ph": "X", "name": "ReadAsync 459", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487174, "dur": 20, "ph": "X", "name": "ReadAsync 185", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487197, "dur": 20, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487219, "dur": 18, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487241, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487262, "dur": 21, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487285, "dur": 16, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487303, "dur": 17, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487323, "dur": 17, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487342, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487359, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487376, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487377, "dur": 15, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487394, "dur": 37, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487433, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487461, "dur": 17, "ph": "X", "name": "ReadAsync 480", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487479, "dur": 16, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487498, "dur": 16, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487516, "dur": 18, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487536, "dur": 14, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487552, "dur": 13, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487567, "dur": 18, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487587, "dur": 16, "ph": "X", "name": "ReadAsync 276", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487606, "dur": 15, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487623, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487641, "dur": 17, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487661, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487677, "dur": 15, "ph": "X", "name": "ReadAsync 363", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487694, "dur": 15, "ph": "X", "name": "ReadAsync 210", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487711, "dur": 15, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487728, "dur": 18, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487749, "dur": 15, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487766, "dur": 10, "ph": "X", "name": "ReadAsync 158", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487778, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487793, "dur": 15, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487810, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487828, "dur": 17, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487848, "dur": 16, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487865, "dur": 16, "ph": "X", "name": "ReadAsync 287", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487884, "dur": 15, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487901, "dur": 15, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487918, "dur": 14, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487933, "dur": 15, "ph": "X", "name": "ReadAsync 66", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487951, "dur": 17, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487969, "dur": 16, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328487987, "dur": 20, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488010, "dur": 16, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488027, "dur": 16, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488046, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488064, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488082, "dur": 14, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488098, "dur": 18, "ph": "X", "name": "ReadAsync 231", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488118, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488138, "dur": 16, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488155, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488172, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488189, "dur": 15, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488207, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488225, "dur": 16, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488243, "dur": 17, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488262, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488279, "dur": 15, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488296, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488312, "dur": 15, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488329, "dur": 16, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488347, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488364, "dur": 15, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488382, "dur": 15, "ph": "X", "name": "ReadAsync 387", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488400, "dur": 15, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488417, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488433, "dur": 16, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488452, "dur": 17, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488471, "dur": 16, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488490, "dur": 16, "ph": "X", "name": "ReadAsync 395", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488508, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488525, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488543, "dur": 15, "ph": "X", "name": "ReadAsync 257", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488559, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488577, "dur": 27, "ph": "X", "name": "ReadAsync 274", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488607, "dur": 29, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488639, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488664, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488682, "dur": 16, "ph": "X", "name": "ReadAsync 166", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488700, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488720, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488738, "dur": 26, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488767, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488792, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488823, "dur": 26, "ph": "X", "name": "ReadAsync 485", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488851, "dur": 16, "ph": "X", "name": "ReadAsync 545", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488869, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488888, "dur": 18, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488909, "dur": 19, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488930, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488957, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488977, "dur": 18, "ph": "X", "name": "ReadAsync 596", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328488997, "dur": 139, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489139, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489176, "dur": 1, "ph": "X", "name": "ProcessMessages 2453", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489178, "dur": 26, "ph": "X", "name": "ReadAsync 2453", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489207, "dur": 22, "ph": "X", "name": "ReadAsync 389", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489232, "dur": 24, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489259, "dur": 14, "ph": "X", "name": "ReadAsync 600", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489275, "dur": 16, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489293, "dur": 14, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489309, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489326, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489344, "dur": 11, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489356, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489372, "dur": 15, "ph": "X", "name": "ReadAsync 224", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489389, "dur": 22, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489413, "dur": 17, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489432, "dur": 14, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489448, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489466, "dur": 16, "ph": "X", "name": "ReadAsync 264", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489484, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489500, "dur": 14, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489517, "dur": 27, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489547, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489578, "dur": 20, "ph": "X", "name": "ReadAsync 629", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489600, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489602, "dur": 19, "ph": "X", "name": "ReadAsync 57", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489623, "dur": 18, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489644, "dur": 22, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489668, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489669, "dur": 27, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489699, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489720, "dur": 16, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489739, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489759, "dur": 16, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489777, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489796, "dur": 20, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489818, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489842, "dur": 16, "ph": "X", "name": "ReadAsync 172", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489860, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489877, "dur": 18, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489897, "dur": 14, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489913, "dur": 16, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489931, "dur": 25, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489958, "dur": 16, "ph": "X", "name": "ReadAsync 551", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489977, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328489998, "dur": 16, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490016, "dur": 15, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490033, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490051, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490071, "dur": 17, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490091, "dur": 14, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490107, "dur": 17, "ph": "X", "name": "ReadAsync 230", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490126, "dur": 16, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490144, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490162, "dur": 15, "ph": "X", "name": "ReadAsync 464", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490179, "dur": 21, "ph": "X", "name": "ReadAsync 468", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490203, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490222, "dur": 22, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490246, "dur": 16, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490264, "dur": 16, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490282, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490320, "dur": 15, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490337, "dur": 15, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490354, "dur": 54, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490410, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490427, "dur": 16, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490445, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490462, "dur": 17, "ph": "X", "name": "ReadAsync 465", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490481, "dur": 15, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490498, "dur": 43, "ph": "X", "name": "ReadAsync 243", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490545, "dur": 26, "ph": "X", "name": "ReadAsync 87", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490574, "dur": 23, "ph": "X", "name": "ReadAsync 1", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490599, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490601, "dur": 27, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490631, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490633, "dur": 20, "ph": "X", "name": "ReadAsync 643", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490656, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490678, "dur": 20, "ph": "X", "name": "ReadAsync 120", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490700, "dur": 23, "ph": "X", "name": "ReadAsync 486", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490726, "dur": 54, "ph": "X", "name": "ReadAsync 627", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490781, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490783, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490809, "dur": 20, "ph": "X", "name": "ReadAsync 936", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490831, "dur": 19, "ph": "X", "name": "ReadAsync 550", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490852, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490870, "dur": 18, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490890, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490911, "dur": 17, "ph": "X", "name": "ReadAsync 474", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490930, "dur": 17, "ph": "X", "name": "ReadAsync 487", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490949, "dur": 15, "ph": "X", "name": "ReadAsync 585", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328490967, "dur": 34, "ph": "X", "name": "ReadAsync 204", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491006, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491064, "dur": 1, "ph": "X", "name": "ProcessMessages 960", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491066, "dur": 27, "ph": "X", "name": "ReadAsync 960", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491095, "dur": 1, "ph": "X", "name": "ProcessMessages 1393", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491097, "dur": 60, "ph": "X", "name": "ReadAsync 1393", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491160, "dur": 29, "ph": "X", "name": "ReadAsync 579", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491190, "dur": 1, "ph": "X", "name": "ProcessMessages 1063", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491192, "dur": 18, "ph": "X", "name": "ReadAsync 1063", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491212, "dur": 16, "ph": "X", "name": "ReadAsync 618", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491230, "dur": 67, "ph": "X", "name": "ReadAsync 110", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491300, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491331, "dur": 1, "ph": "X", "name": "ProcessMessages 1676", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491334, "dur": 62, "ph": "X", "name": "ReadAsync 1676", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491398, "dur": 25, "ph": "X", "name": "ReadAsync 691", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491425, "dur": 1, "ph": "X", "name": "ProcessMessages 1664", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491427, "dur": 18, "ph": "X", "name": "ReadAsync 1664", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491447, "dur": 18, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491467, "dur": 17, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491486, "dur": 22, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491510, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491512, "dur": 27, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491541, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491542, "dur": 21, "ph": "X", "name": "ReadAsync 767", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491566, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491568, "dur": 20, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491590, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491608, "dur": 41, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491650, "dur": 32, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491683, "dur": 1, "ph": "X", "name": "ProcessMessages 1716", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491685, "dur": 16, "ph": "X", "name": "ReadAsync 1716", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491705, "dur": 111, "ph": "X", "name": "ReadAsync 189", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491819, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491846, "dur": 56, "ph": "X", "name": "ReadAsync 1195", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491906, "dur": 26, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491933, "dur": 1, "ph": "X", "name": "ProcessMessages 1639", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491934, "dur": 17, "ph": "X", "name": "ReadAsync 1639", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491953, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328491972, "dur": 35, "ph": "X", "name": "ReadAsync 476", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492009, "dur": 17, "ph": "X", "name": "ReadAsync 489", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492028, "dur": 36, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492066, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492090, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492091, "dur": 16, "ph": "X", "name": "ReadAsync 1166", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492110, "dur": 43, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492155, "dur": 21, "ph": "X", "name": "ReadAsync 604", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492177, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492179, "dur": 15, "ph": "X", "name": "ReadAsync 1126", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492196, "dur": 16, "ph": "X", "name": "ReadAsync 369", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492214, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492232, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492251, "dur": 17, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492270, "dur": 16, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492288, "dur": 18, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492308, "dur": 16, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492326, "dur": 32, "ph": "X", "name": "ReadAsync 412", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492360, "dur": 18, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492381, "dur": 10, "ph": "X", "name": "ReadAsync 1034", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492393, "dur": 15, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492409, "dur": 16, "ph": "X", "name": "ReadAsync 151", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492428, "dur": 14, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492444, "dur": 17, "ph": "X", "name": "ReadAsync 249", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492463, "dur": 15, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492480, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492497, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492516, "dur": 38, "ph": "X", "name": "ReadAsync 588", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492555, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492557, "dur": 22, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492582, "dur": 10, "ph": "X", "name": "ReadAsync 1179", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492594, "dur": 17, "ph": "X", "name": "ReadAsync 179", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492614, "dur": 17, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492632, "dur": 19, "ph": "X", "name": "ReadAsync 396", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492654, "dur": 16, "ph": "X", "name": "ReadAsync 600", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492672, "dur": 16, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492691, "dur": 16, "ph": "X", "name": "ReadAsync 401", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492709, "dur": 29, "ph": "X", "name": "ReadAsync 437", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492740, "dur": 19, "ph": "X", "name": "ReadAsync 497", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492761, "dur": 15, "ph": "X", "name": "ReadAsync 883", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492778, "dur": 14, "ph": "X", "name": "ReadAsync 337", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492795, "dur": 16, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492813, "dur": 16, "ph": "X", "name": "ReadAsync 498", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492831, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492848, "dur": 15, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492866, "dur": 16, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492884, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492904, "dur": 15, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492922, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492940, "dur": 18, "ph": "X", "name": "ReadAsync 494", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492960, "dur": 16, "ph": "X", "name": "ReadAsync 544", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492979, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328492996, "dur": 16, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493014, "dur": 16, "ph": "X", "name": "ReadAsync 531", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493032, "dur": 15, "ph": "X", "name": "ReadAsync 455", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493049, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493070, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493088, "dur": 16, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493106, "dur": 20, "ph": "X", "name": "ReadAsync 470", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493128, "dur": 16, "ph": "X", "name": "ReadAsync 481", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493146, "dur": 17, "ph": "X", "name": "ReadAsync 482", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493166, "dur": 15, "ph": "X", "name": "ReadAsync 504", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493183, "dur": 15, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493200, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493219, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493237, "dur": 16, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493255, "dur": 19, "ph": "X", "name": "ReadAsync 390", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493276, "dur": 15, "ph": "X", "name": "ReadAsync 411", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493293, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493312, "dur": 16, "ph": "X", "name": "ReadAsync 510", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493330, "dur": 16, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493347, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493349, "dur": 25, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493376, "dur": 16, "ph": "X", "name": "ReadAsync 698", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493394, "dur": 15, "ph": "X", "name": "ReadAsync 43", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493413, "dur": 66, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493483, "dur": 18, "ph": "X", "name": "ReadAsync 631", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493505, "dur": 17, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493524, "dur": 17, "ph": "X", "name": "ReadAsync 428", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493553, "dur": 23, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493578, "dur": 1, "ph": "X", "name": "ProcessMessages 994", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493580, "dur": 18, "ph": "X", "name": "ReadAsync 994", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493601, "dur": 24, "ph": "X", "name": "ReadAsync 429", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493627, "dur": 16, "ph": "X", "name": "ReadAsync 615", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493645, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493670, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493672, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493700, "dur": 25, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493727, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493729, "dur": 21, "ph": "X", "name": "ReadAsync 706", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493752, "dur": 18, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493773, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493792, "dur": 16, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493811, "dur": 26, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493840, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493862, "dur": 17, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493881, "dur": 18, "ph": "X", "name": "ReadAsync 235", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493902, "dur": 16, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493920, "dur": 16, "ph": "X", "name": "ReadAsync 103", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493937, "dur": 16, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493955, "dur": 16, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493974, "dur": 15, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328493991, "dur": 16, "ph": "X", "name": "ReadAsync 368", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494009, "dur": 17, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494029, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494048, "dur": 16, "ph": "X", "name": "ReadAsync 496", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494067, "dur": 16, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494085, "dur": 16, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494103, "dur": 15, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494121, "dur": 17, "ph": "X", "name": "ReadAsync 382", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494141, "dur": 15, "ph": "X", "name": "ReadAsync 339", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494158, "dur": 14, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494175, "dur": 22, "ph": "X", "name": "ReadAsync 133", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494201, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494222, "dur": 22, "ph": "X", "name": "ReadAsync 45", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494246, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494248, "dur": 25, "ph": "X", "name": "ReadAsync 409", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494275, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494303, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328494305, "dur": 24987, "ph": "X", "name": "ReadAsync 159", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519298, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519301, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519342, "dur": 455, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519802, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519893, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519896, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519934, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328519937, "dur": 327, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520269, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520308, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520311, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520436, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520438, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520492, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520495, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520530, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520532, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520814, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520853, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520854, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520892, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520931, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520933, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328520998, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521057, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521059, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521098, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521100, "dur": 198, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521301, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521302, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521335, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521449, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521480, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521482, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521545, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521580, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521582, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521618, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521620, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521653, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521655, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521821, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521855, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328521856, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522004, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522054, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522056, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522123, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522171, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522174, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522200, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522374, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522405, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522407, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522436, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522535, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522571, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522572, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522605, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522607, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522638, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522640, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522690, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522725, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522727, "dur": 199, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522931, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522982, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328522984, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523026, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523029, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523061, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523118, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523155, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523195, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523198, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523226, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523228, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523268, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523270, "dur": 217, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523492, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523524, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523564, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523591, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523618, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523650, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523652, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523682, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523684, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523717, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523752, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523785, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523787, "dur": 27, "ph": "X", "name": "ReadAsync 28", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523817, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523819, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328523845, "dur": 227, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524077, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524110, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524145, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524185, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524219, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524253, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524293, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524295, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524331, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524370, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524399, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524431, "dur": 212, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524647, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524682, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524739, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524777, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524810, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524846, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524878, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524880, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524912, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524948, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328524950, "dur": 240, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525195, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525228, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525230, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525270, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525298, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525347, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525379, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525381, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525425, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525455, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525485, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525487, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525512, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525604, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525633, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525929, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525968, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328525970, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526035, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526070, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526072, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526112, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526114, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526145, "dur": 244, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526393, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526424, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526499, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526536, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526585, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526614, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526674, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526676, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526720, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526722, "dur": 134, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328526861, "dur": 233, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527097, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527099, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527148, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527151, "dur": 171, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527326, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527395, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527398, "dur": 243, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527643, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527645, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527710, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527712, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527743, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527745, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527778, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527805, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527834, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527869, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527936, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527964, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328527966, "dur": 168, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528139, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528172, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528176, "dur": 436, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528617, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528658, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328528661, "dur": 6664, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328535333, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328535336, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328535396, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328535403, "dur": 2014, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328537421, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328537460, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328537463, "dur": 1104, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328538571, "dur": 419, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328538993, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328538996, "dur": 5201, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328544201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328544204, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328544236, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328544239, "dur": 4233, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328548478, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328548481, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328548523, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328548527, "dur": 1119, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328549651, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328549688, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328549690, "dur": 5068, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328554763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328554765, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328554827, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328554833, "dur": 8049, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328562887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328562890, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328562989, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328562992, "dur": 5290, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568290, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568339, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568342, "dur": 375, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568722, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568755, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328568757, "dur": 6239, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328575008, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328575013, "dur": 2713, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328577737, "dur": 25, "ph": "X", "name": "ProcessMessages 162", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328577764, "dur": 3501, "ph": "X", "name": "ReadAsync 162", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581273, "dur": 12, "ph": "X", "name": "ProcessMessages 160", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581289, "dur": 351, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581643, "dur": 35, "ph": "X", "name": "ProcessMessages 656", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581679, "dur": 184, "ph": "X", "name": "ReadAsync 656", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328581869, "dur": 624, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582499, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582568, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582571, "dur": 342, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582917, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582972, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328582974, "dur": 88, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583068, "dur": 217, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583290, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583408, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583411, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583486, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583489, "dur": 27, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583519, "dur": 7, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583529, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583590, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583631, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583819, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583862, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583863, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583906, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328583991, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584022, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584065, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584067, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584169, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584198, "dur": 378, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584591, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584628, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584631, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584685, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584725, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584788, "dur": 160, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584952, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584983, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328584985, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585146, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585196, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585252, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585380, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585382, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585420, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585422, "dur": 119, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585546, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585590, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585803, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585838, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585872, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585958, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585986, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328585989, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586027, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586084, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586086, "dur": 516, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586607, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586654, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586656, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586769, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586835, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328586838, "dur": 240, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587082, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587157, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587159, "dur": 199, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587363, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587402, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587404, "dur": 169, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587580, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587612, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587616, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587853, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587883, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587885, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328587918, "dur": 399, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588322, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588387, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588489, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588521, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588523, "dur": 461, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588987, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328588989, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589029, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589031, "dur": 123, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589159, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589196, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589355, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589398, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589499, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589534, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589536, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589608, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589643, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589721, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589754, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589759, "dur": 222, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328589985, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590016, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590017, "dur": 572, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590595, "dur": 152, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590750, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590753, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590814, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590816, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328590843, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591021, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591057, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591060, "dur": 207, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591270, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591273, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591321, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591323, "dur": 671, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328591998, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592031, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592033, "dur": 183, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592222, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592265, "dur": 602, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592872, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592922, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328592925, "dur": 658, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328593588, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328593651, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328593654, "dur": 1259, "ph": "X", "name": "ReadAsync 132", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328594922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328594925, "dur": 308, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595237, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595239, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595310, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595313, "dur": 86, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595402, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328595404, "dur": 1006, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596415, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596475, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596479, "dur": 175, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596662, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596772, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328596981, "dur": 240, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597225, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597227, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597267, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597269, "dur": 153, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597427, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597484, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597669, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597702, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597704, "dur": 119, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597829, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597857, "dur": 108, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328597972, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328598007, "dur": 1049, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599061, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599161, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599164, "dur": 286, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599481, "dur": 160, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599656, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599659, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599730, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599732, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599798, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599800, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599857, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599938, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328599941, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600014, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600016, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600103, "dur": 22, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600127, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600365, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600429, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600432, "dur": 95, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600532, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600575, "dur": 333, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600913, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600955, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328600957, "dur": 500, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601462, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601592, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601594, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601685, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601687, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328601723, "dur": 456, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602185, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602226, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602230, "dur": 241, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602476, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602512, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602514, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602698, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602775, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602778, "dur": 110, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602892, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602896, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602953, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328602955, "dur": 136, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603097, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603177, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603180, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603235, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603238, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603443, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328603445, "dur": 2744, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328606197, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328606201, "dur": 387, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328606592, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328606607, "dur": 6224, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328612848, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328612856, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328612909, "dur": 23, "ph": "X", "name": "ProcessMessages 218", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328612933, "dur": 2197, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328615138, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328615144, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328615205, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328615222, "dur": 16108, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328631340, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328631344, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328631402, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328631417, "dur": 137984, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328769411, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328769416, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328769482, "dur": 41, "ph": "X", "name": "ProcessMessages 444", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328769525, "dur": 11513, "ph": "X", "name": "ReadAsync 444", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328781047, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328781052, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328781101, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328781115, "dur": 29834, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328810956, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328810959, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328811001, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328811006, "dur": 1101, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812112, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812166, "dur": 12, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812179, "dur": 470, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812654, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812690, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 93864, "tid": 81604378624, "ts": 1754339328812692, "dur": 9463, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 93864, "tid": 49661901, "ts": 1754339328823217, "dur": 1471, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 93864, "tid": 77309411328, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 93864, "tid": 77309411328, "ts": 1754339328441224, "dur": 380973, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 93864, "tid": 77309411328, "ts": 1754339328441316, "dur": 8692, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 93864, "tid": 77309411328, "ts": 1754339328822202, "dur": 9, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 93864, "tid": 77309411328, "ts": 1754339328822213, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 93864, "tid": 49661901, "ts": 1754339328824692, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 93864, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 93864, "tid": 1, "ts": 1754339328269019, "dur": 1517, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754339328270539, "dur": 169133, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 93864, "tid": 1, "ts": 1754339328439675, "dur": 1530, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 93864, "tid": 49661901, "ts": 1754339328824701, "dur": 8, "ph": "X", "name": "", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1754339328482613, "dur":2558, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328485181, "dur":496, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328485763, "dur":389, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328490706, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProBuilder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754339328490928, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.ProGrids-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754339328491053, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Recorder-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1754339328493859, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestAudioModule-FeaturesChecked.txt_5ujt.info" }}
,{ "pid":12345, "tid":0, "ts":1754339328494589, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":0, "ts":1754339328486202, "dur":8509, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328494717, "dur":317856, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328812575, "dur":192, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328813072, "dur":3771, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1754339328486390, "dur":8334, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328499632, "dur":422, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Assets/StreamingAssets" }}
,{ "pid":12345, "tid":1, "ts":1754339328500054, "dur":1461, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1754339328501515, "dur":12938, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1754339328514454, "dur":294, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport" }}
,{ "pid":12345, "tid":1, "ts":1754339328514749, "dur":3420, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono" }}
,{ "pid":12345, "tid":1, "ts":1754339328518169, "dur":266, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/Variations/win64_player_nondevelopment_mono/Data/Resources" }}
,{ "pid":12345, "tid":1, "ts":1754339328518436, "dur":471, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.1.1f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1754339328518908, "dur":416, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Win642/Data" }}
,{ "pid":12345, "tid":1, "ts":1754339328519324, "dur":88, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/Plugins" }}
,{ "pid":12345, "tid":1, "ts":1754339328519412, "dur":92, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1754339328494733, "dur":24787, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328519535, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/7893594715672875865.rsp" }}
,{ "pid":12345, "tid":1, "ts":1754339328519593, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328519929, "dur":679, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328520612, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328520750, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328520917, "dur":144, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328521065, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328521180, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328521362, "dur":425, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328521789, "dur":496, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328522287, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328522473, "dur":669, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328523144, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328523267, "dur":152, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328523425, "dur":512, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328523939, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328524158, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328524380, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328524582, "dur":193, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328524782, "dur":164, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328524950, "dur":108, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328525062, "dur":376, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328525439, "dur":683, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328526123, "dur":111, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.Shared.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328526239, "dur":146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328526391, "dur":262, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328526655, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328526999, "dur":195, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328527197, "dur":2282, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328529485, "dur":191, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328529678, "dur":226, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328529906, "dur":512, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328530419, "dur":436, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328530858, "dur":459, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328531319, "dur":999, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328532320, "dur":723, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328533046, "dur":390, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328533439, "dur":481, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328534659, "dur":247, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\MethodsToPreserve.xml" }}
,{ "pid":12345, "tid":1, "ts":1754339328534909, "dur":237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\TypesInScenes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754339328535148, "dur":270, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\SerializedTypes.xml" }}
,{ "pid":12345, "tid":1, "ts":1754339328535421, "dur":357, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\artifacts\\UnityLinkerInputs\\EditorToUnityLinkerData.json" }}
,{ "pid":12345, "tid":1, "ts":1754339328519707, "dur":16449, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker C:/Unity/BLAME/BLAME/Library/Bee/artifacts/unitylinker_dwek.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1754339328536158, "dur":13900, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328557881, "dur":17060, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":1, "ts":1754339328574943, "dur":447, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328577225, "dur":128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":1, "ts":1754339328577354, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328577522, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328578021, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328578207, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328578374, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328578512, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328578660, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328579459, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328581337, "dur":651, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328583277, "dur":412, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328584701, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328584826, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328584955, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328585089, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328585218, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328585373, "dur":387, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328585802, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328587407, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328587754, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328589349, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328590775, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328591705, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328592154, "dur":1053, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328593211, "dur":65, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.ARModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":1, "ts":1754339328593276, "dur":664, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328595108, "dur":326, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328595440, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328595722, "dur":373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328596138, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328597837, "dur":546, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328599236, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328599440, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328599672, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328599820, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328599952, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328600116, "dur":275, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328601360, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1754339328603097, "dur":9214, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1" }}
,{ "pid":12345, "tid":1, "ts":1754339328612693, "dur":199886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328486427, "dur":8314, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328494748, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WindowsDesktop.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328495272, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.WebGL.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328495890, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.VisionOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328496418, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.UniversalWindows.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328497021, "dur":523, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.MacOSX.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328497544, "dur":625, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Linux.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328498169, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.iOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328498765, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.EmbeddedLinux.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328499346, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328499982, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.AppleTV.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328500654, "dur":854, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.BuildLogic.Android.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328501509, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.xml" }}
,{ "pid":12345, "tid":2, "ts":1754339328502064, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.Output.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328502655, "dur":540, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328503196, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Cecil.Awesome.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328504654, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XPath.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328505259, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlSerializer.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328505836, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XmlDocument.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328494748, "dur":11668, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328506416, "dur":549, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Bee.Toolchain.TvOS.dll" }}
,{ "pid":12345, "tid":2, "ts":1754339328506416, "dur":3071, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328509487, "dur":4577, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328514064, "dur":3211, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328517275, "dur":2248, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328519540, "dur":674, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328520226, "dur":453, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328520690, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328521238, "dur":478, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328521722, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328522244, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328522793, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328523354, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328523911, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328524499, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328525072, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328525615, "dur":728, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328526350, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328526817, "dur":451, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328527273, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":2, "ts":1754339328527512, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328527647, "dur":384, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":2, "ts":1754339328528032, "dur":239, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328563304, "dur":16793, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.resource" }}
,{ "pid":12345, "tid":2, "ts":1754339328580248, "dur":1137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328581385, "dur":95, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.MarshallingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754339328582568, "dur":311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328582886, "dur":213, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328583108, "dur":113, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328583240, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328583463, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328583857, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/Unity.Collections-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1754339328583911, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328585397, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328585777, "dur":185, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328586003, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328587388, "dur":309, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328587737, "dur":515, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328589369, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328590945, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328591120, "dur":241, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328591370, "dur":340, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328591715, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328592482, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328592960, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328594630, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328595906, "dur":176, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328597049, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328598575, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328600075, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328600391, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328600738, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328602437, "dur":597, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1754339328603658, "dur":9341, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":2, "ts":1754339328613129, "dur":199451, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328486412, "dur":8320, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328495485, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1754339328496092, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.exe" }}
,{ "pid":12345, "tid":3, "ts":1754339328496628, "dur":614, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1754339328497242, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328497827, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\UnityLinker.deps.json" }}
,{ "pid":12345, "tid":3, "ts":1754339328498450, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.TinyProfiler.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328499028, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Options.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328499610, "dur":594, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.xml" }}
,{ "pid":12345, "tid":3, "ts":1754339328500204, "dur":814, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.Output.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328501018, "dur":642, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328501660, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Shell.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328502234, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Compile.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328502801, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common35.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328503337, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328504024, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328504583, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":3, "ts":1754339328494741, "dur":10438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328505180, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.dll.config" }}
,{ "pid":12345, "tid":3, "ts":1754339328505735, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.runtimeconfig.json" }}
,{ "pid":12345, "tid":3, "ts":1754339328506264, "dur":599, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp-compile.exe" }}
,{ "pid":12345, "tid":3, "ts":1754339328505180, "dur":3870, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328509051, "dur":4609, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328513660, "dur":3762, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328517422, "dur":2100, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328519540, "dur":675, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328520225, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328520735, "dur":589, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328521333, "dur":547, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328521885, "dur":530, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328522424, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328522846, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328523432, "dur":591, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328524030, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328524603, "dur":623, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328525234, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328525770, "dur":662, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328526439, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328526979, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328527333, "dur":188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":3, "ts":1754339328527522, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328527641, "dur":272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":3, "ts":1754339328527914, "dur":242, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328548881, "dur":31795, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":3, "ts":1754339328580802, "dur":117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754339328580920, "dur":556, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328582565, "dur":362, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328583807, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328585538, "dur":279, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328585854, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328587397, "dur":365, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328587808, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328589433, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328591109, "dur":296, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328591412, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328591713, "dur":1538, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328593289, "dur":716, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328595143, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.UnityWebRequestTextureModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1754339328595249, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328596139, "dur":356, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328597489, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328597645, "dur":436, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328599370, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328600544, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328601856, "dur":628, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1754339328603341, "dur":9713, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0" }}
,{ "pid":12345, "tid":3, "ts":1754339328613187, "dur":199402, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328486448, "dur":8300, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328494755, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.XDocument.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328495334, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Serialization.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328495956, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.ReaderWriter.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328496484, "dur":606, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328497090, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Xml.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328497663, "dur":659, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Windows.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328498322, "dur":545, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.HttpUtility.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328498867, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Web.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328499446, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ValueTuple.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328500046, "dur":686, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.Local.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328500733, "dur":832, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Transactions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328501565, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Timer.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328502167, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.ThreadPool.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328502739, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Thread.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328503271, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Parallel.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328503873, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Extensions.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328504427, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328505031, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Tasks.Dataflow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328505626, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Overlapped.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328506202, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328494755, "dur":12044, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328507336, "dur":539, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328513519, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Windows.Forms.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328506799, "dur":7363, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328514163, "dur":2876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328517039, "dur":2482, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328519576, "dur":723, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328520317, "dur":135, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754339328520465, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win642\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":4, "ts":1754339328520690, "dur":154, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.BuildTestAssets.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328520846, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Autodesk.Fbx.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328520999, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\BakeryRuntimeAssembly.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328521129, "dur":144, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Domain_Reload.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328521274, "dur":237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Tayx.Graphy.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328521512, "dur":314, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328521828, "dur":488, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328522318, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Formats.Fbx.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328522494, "dur":723, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328523219, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328523417, "dur":197, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Csg.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328523616, "dur":312, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328523930, "dur":222, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.KdTree.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328524154, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Poly2Tri.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328524377, "dur":200, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProBuilder.Stl.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328524582, "dur":206, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.ProGrids.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328524793, "dur":189, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.Base.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328524984, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Recorder.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328525145, "dur":348, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Rendering.LightTransport.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328525495, "dur":752, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328526249, "dur":159, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Samples.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328526410, "dur":232, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328526648, "dur":337, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.GPUDriven.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328526987, "dur":211, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Config.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328527200, "dur":2244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328529447, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.HighDefinition.Samples.Common.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328529632, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328529855, "dur":538, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328530395, "dur":424, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328530829, "dur":471, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualEffectGraph.Runtime.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328531306, "dur":1069, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328532378, "dur":649, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328533031, "dur":398, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328533433, "dur":525, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Unity\\BLAME\\BLAME\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":4, "ts":1754339328520303, "dur":14400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754339328535506, "dur":151, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1754339328537327, "dur":232427, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754339328811176, "dur":147, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754339328811086, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":4, "ts":1754339328811346, "dur":1193, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/boot.config" }}
,{ "pid":12345, "tid":5, "ts":1754339328486472, "dur":8283, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328494764, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Threading.Channels.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328495368, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.RegularExpressions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328495955, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Json.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328496541, "dur":601, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encodings.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328497142, "dur":558, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.Extensions.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328497700, "dur":636, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328498336, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Text.Encoding.CodePages.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328498947, "dur":580, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceProcess.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328499527, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ServiceModel.Web.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328500115, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.SecureString.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328500913, "dur":687, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.Windows.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328501600, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Principal.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328502185, "dur":563, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328502749, "dur":529, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.X509Certificates.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328503279, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Primitives.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328504375, "dur":524, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Encoding.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328504899, "dur":624, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328505523, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Csp.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328506074, "dur":555, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Cng.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328494764, "dur":11865, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328506629, "dur":2717, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328509347, "dur":4285, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328513632, "dur":3886, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328517519, "dur":2015, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328519549, "dur":784, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328520338, "dur":529, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328520872, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328521493, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328522048, "dur":518, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328522571, "dur":464, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328523041, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328523656, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328524250, "dur":577, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328524832, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328525347, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328525917, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328526511, "dur":566, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328527080, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328527303, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328527432, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":5, "ts":1754339328527678, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328527864, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/D3D12/D3D12Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1754339328527986, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1754339328529039, "dur":252396, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets.resS" }}
,{ "pid":12345, "tid":5, "ts":1754339328781547, "dur":31028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328486499, "dur":8264, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328494770, "dur":605, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Cryptography.Algorithms.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328495376, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.Claims.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328495999, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Security.AccessControl.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328496567, "dur":646, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Xml.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328497214, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Primitives.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328497804, "dur":617, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Json.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328498421, "dur":607, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.Formatters.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328499029, "dur":648, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328499677, "dur":690, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Numerics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328500367, "dur":874, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Loader.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328501241, "dur":537, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Intrinsics.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328501778, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.RuntimeInformation.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328502314, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.JavaScript.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328502850, "dur":568, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.InteropServices.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328503418, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Handles.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328504026, "dur":616, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.Extensions.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328504643, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328505253, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.VisualC.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328505807, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Runtime.CompilerServices.Unsafe.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328506366, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Writer.dll" }}
,{ "pid":12345, "tid":6, "ts":1754339328494769, "dur":12171, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328506940, "dur":7737, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328514677, "dur":3828, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328518506, "dur":1075, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328519584, "dur":731, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328520324, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328520851, "dur":588, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328521445, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328521968, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328522512, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328522965, "dur":616, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328523586, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328524151, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328524715, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328525297, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328525866, "dur":635, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328526506, "dur":550, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328527061, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328527390, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":6, "ts":1754339328527539, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328527654, "dur":282, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":6, "ts":1754339328527936, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328528248, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328569144, "dur":10498, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets" }}
,{ "pid":12345, "tid":6, "ts":1754339328579998, "dur":290, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328581476, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328582972, "dur":184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328583164, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328583295, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328583651, "dur":210, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328583901, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328585305, "dur":342, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328585688, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328587052, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328587188, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328587319, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328587515, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328588857, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328590559, "dur":467, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328591579, "dur":455, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328592044, "dur":980, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328593061, "dur":856, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328595024, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328595324, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328595685, "dur":474, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328596166, "dur":191, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328596365, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328596627, "dur":250, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328596915, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328598527, "dur":497, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328599900, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328600023, "dur":247, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328600289, "dur":523, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328600849, "dur":462, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1754339328602573, "dur":3490, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level2" }}
,{ "pid":12345, "tid":6, "ts":1754339328606208, "dur":206364, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328486528, "dur":8241, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328494775, "dur":657, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.ResourceManager.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328495433, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Resources.Reader.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328496043, "dur":531, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.TypeExtensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328496574, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Primitives.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328497205, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Metadata.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328497760, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Extensions.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328498390, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.Lightweight.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328498976, "dur":577, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.ILGeneration.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328499553, "dur":630, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.Emit.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328500184, "dur":791, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328500975, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Reflection.DispatchProxy.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328501622, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.Linq.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328502226, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Xml.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328502804, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.Uri.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328503375, "dur":647, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.DataContractSerialization.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328504022, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Private.CoreLib.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328504596, "dur":597, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ObjectModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328505193, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.Vectors.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328505768, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Numerics.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328506329, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328494774, "dur":12122, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328513389, "dur":520, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IdentityModel.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328506896, "dur":7912, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328514809, "dur":3896, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328518706, "dur":831, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328519545, "dur":730, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328520284, "dur":558, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328520851, "dur":630, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328521493, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328522007, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328522554, "dur":486, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328523044, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328523615, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328524209, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328524796, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328525326, "dur":570, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328525900, "dur":607, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328526515, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328527084, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":7, "ts":1754339328527257, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328527393, "dur":197, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":7, "ts":1754339328527591, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328527706, "dur":341, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":7, "ts":1754339328528047, "dur":218, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328570661, "dur":2613, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":7, "ts":1754339328573422, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328573603, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328573741, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Managed/UnityEngine.VehiclesModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1754339328573886, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328574022, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328574202, "dur":139, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328574718, "dur":126, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.TLSModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754339328574845, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328575280, "dur":182, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328575719, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328576145, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328576683, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328577092, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328577258, "dur":129, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328577912, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328578068, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328578235, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328578417, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328578792, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328579628, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328580024, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.AssetBundleModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1754339328580079, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328581993, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328583270, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328583547, "dur":286, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328583870, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328585322, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328586358, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328587702, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328589338, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328590652, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328591661, "dur":552, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328592219, "dur":976, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328593233, "dur":734, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328595137, "dur":389, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328595532, "dur":712, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328596251, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328596422, "dur":293, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328596726, "dur":285, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328597053, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328598607, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328600034, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328600362, "dur":330, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328600729, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328602562, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1754339328603781, "dur":208795, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328486557, "dur":8220, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328494784, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328495418, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebProxy.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328496014, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebHeaderCollection.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328496567, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebClient.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328497217, "dur":567, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Sockets.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328497785, "dur":653, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.ServicePoint.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328498439, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Security.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328499027, "dur":603, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Requests.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328499630, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Quic.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328500206, "dur":798, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Primitives.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328501004, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Ping.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328501632, "dur":589, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NetworkInformation.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328502221, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.NameResolution.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328502773, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Mail.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328503305, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.HttpListener.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328504378, "dur":575, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328504953, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328505546, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Memory.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328506141, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Queryable.dll" }}
,{ "pid":12345, "tid":8, "ts":1754339328494784, "dur":11949, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328506733, "dur":2572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328509305, "dur":4336, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328513641, "dur":3839, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328517480, "dur":2052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328519542, "dur":709, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328520260, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328520765, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328521335, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328521903, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328522454, "dur":399, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328522858, "dur":590, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328523454, "dur":614, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328524076, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328524641, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328525237, "dur":549, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328525791, "dur":641, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328526441, "dur":537, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328526984, "dur":391, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328527379, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":8, "ts":1754339328527623, "dur":121, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328527749, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":8, "ts":1754339328527885, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328529011, "dur":51160, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets2.assets" }}
,{ "pid":12345, "tid":8, "ts":1754339328580367, "dur":1005, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328582193, "dur":580, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328583587, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328583737, "dur":485, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328585322, "dur":333, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328585695, "dur":576, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328587574, "dur":417, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328588923, "dur":476, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328590591, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328591699, "dur":1197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328592903, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1754339328593051, "dur":794, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328594952, "dur":313, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328595905, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328596173, "dur":378, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328597596, "dur":438, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328599240, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328600517, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328601380, "dur":585, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1754339328603064, "dur":9701, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level1.resS" }}
,{ "pid":12345, "tid":8, "ts":1754339328612915, "dur":199662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328486584, "dur":8200, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328494791, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Parallel.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328495381, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.Expressions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328495993, "dur":566, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Linq.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328496559, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.UnmanagedMemoryStream.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328497213, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328497805, "dur":604, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Pipes.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328498410, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.MemoryMappedFiles.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328498982, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.IsolatedStorage.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328499556, "dur":623, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Watcher.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328500179, "dur":772, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.Primitives.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328500951, "dur":654, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.DriveInfo.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328501606, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328502188, "dur":585, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.AccessControl.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328502774, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328503303, "dur":695, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.ZipFile.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328503999, "dur":517, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Native.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328504516, "dur":578, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328505095, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328505667, "dur":587, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.Compression.Brotli.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328506255, "dur":608, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Extensions.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328494791, "dur":12072, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328513466, "dur":628, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Web.ApplicationServices.dll" }}
,{ "pid":12345, "tid":9, "ts":1754339328506864, "dur":7877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328514741, "dur":3919, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328518660, "dur":878, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328519543, "dur":700, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328520249, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328520746, "dur":564, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328521318, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328521871, "dur":535, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328522414, "dur":405, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328522825, "dur":586, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328523417, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328523990, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328524561, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328525161, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328525697, "dur":681, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328526390, "dur":525, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328526922, "dur":354, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328527281, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":9, "ts":1754339328527412, "dur":108, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328527524, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":9, "ts":1754339328527709, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328527902, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME.exe" }}
,{ "pid":12345, "tid":9, "ts":1754339328528011, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328544610, "dur":35993, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1754339328580756, "dur":706, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328581462, "dur":85, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":9, "ts":1754339328582792, "dur":519, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328584180, "dur":592, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328585597, "dur":190, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328586924, "dur":449, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328588224, "dur":514, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328589826, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328591206, "dur":388, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328591604, "dur":669, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328592280, "dur":905, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328593221, "dur":705, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328595025, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328595327, "dur":372, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328595706, "dur":432, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328596144, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328596306, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328596485, "dur":246, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328596771, "dur":638, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328598651, "dur":759, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328600201, "dur":405, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328600647, "dur":282, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328601967, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1754339328603489, "dur":12056, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":9, "ts":1754339328615675, "dur":196908, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328486617, "dur":8173, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328494796, "dur":620, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328495416, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Globalization.Calendars.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328496054, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328496604, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Asn1.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328497241, "dur":595, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Dynamic.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328497837, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.Primitives.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328498482, "dur":562, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Drawing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328499044, "dur":638, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328499682, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tracing.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328500393, "dur":881, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TraceSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328501275, "dur":501, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Tools.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328501776, "dur":534, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.TextWriterTraceListener.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328502311, "dur":528, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.StackTrace.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328502839, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Process.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328503400, "dur":622, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.FileVersionInfo.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328504023, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.DiagnosticSource.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328504595, "dur":610, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Debug.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328505205, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Diagnostics.Contracts.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328505788, "dur":553, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328506342, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328494796, "dur":12118, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328513525, "dur":634, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Configuration.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328506914, "dur":8111, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328515025, "dur":317, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328515343, "dur":4182, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328519543, "dur":743, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328520297, "dur":539, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328520842, "dur":593, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328521441, "dur":522, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328521970, "dur":543, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328522520, "dur":433, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328522961, "dur":573, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328523542, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328524111, "dur":569, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328524685, "dur":583, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328525274, "dur":565, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328525846, "dur":646, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328526497, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328527048, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328527403, "dur":269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":10, "ts":1754339328527673, "dur":159, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328527836, "dur":144, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":10, "ts":1754339328527980, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328537839, "dur":43021, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets1.assets" }}
,{ "pid":12345, "tid":10, "ts":1754339328581018, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328581435, "dur":146, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1754339328582588, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328582883, "dur":416, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328584219, "dur":568, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328585404, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328585787, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328586019, "dur":423, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328587353, "dur":305, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328587667, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328587818, "dur":494, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328589382, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328590889, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328591026, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328591161, "dur":235, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328591403, "dur":270, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328591680, "dur":442, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328592128, "dur":1064, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328593235, "dur":693, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328595103, "dur":341, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328595451, "dur":276, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328595736, "dur":374, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328596153, "dur":526, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328597737, "dur":457, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328599243, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328599450, "dur":370, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328600410, "dur":295, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328600744, "dur":528, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328602559, "dur":501, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1754339328603790, "dur":208779, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328486641, "dur":8166, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328494813, "dur":583, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Data.Common.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328495396, "dur":611, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Core.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328496007, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Console.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328496561, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Configuration.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328497149, "dur":561, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.TypeConverter.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328497710, "dur":627, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Primitives.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328498337, "dur":591, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.EventBasedAsync.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328498928, "dur":527, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328499456, "dur":613, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.DataAnnotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328500069, "dur":714, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.ComponentModel.Annotations.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328500783, "dur":775, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Specialized.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328501558, "dur":708, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.NonGeneric.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328502266, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Immutable.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328502826, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328503385, "dur":626, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Collections.Concurrent.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328504011, "dur":538, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Buffers.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328504549, "dur":618, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.AppContext.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328505167, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\SharpYaml.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328505741, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\NiceIO.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328506312, "dur":574, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328494813, "dur":12073, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328513418, "dur":643, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Reactive.Windows.Forms.dll" }}
,{ "pid":12345, "tid":11, "ts":1754339328506886, "dur":8099, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328514985, "dur":2797, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328517782, "dur":1793, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328519577, "dur":715, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328520300, "dur":517, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328520822, "dur":587, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328521418, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328521985, "dur":545, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328522536, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328523011, "dur":581, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328523598, "dur":596, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328524199, "dur":540, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328524744, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328525307, "dur":571, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328525883, "dur":627, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328526517, "dur":555, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328527080, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328527234, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":11, "ts":1754339328527480, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328527631, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":11, "ts":1754339328527781, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328527920, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/BLAME_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":11, "ts":1754339328528062, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328528213, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328568694, "dur":11830, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":11, "ts":1754339328580707, "dur":694, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328581402, "dur":140, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.WindModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1754339328582556, "dur":226, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328582821, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328584048, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328585406, "dur":376, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328585835, "dur":510, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328587274, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328587405, "dur":307, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328587752, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328589516, "dur":621, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328591073, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328591227, "dur":398, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328591634, "dur":448, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328592088, "dur":936, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328593060, "dur":818, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328595053, "dur":373, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328596030, "dur":350, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328597192, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328597342, "dur":465, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328598717, "dur":505, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328600078, "dur":308, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328600394, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328600737, "dur":511, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328602334, "dur":554, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1754339328603559, "dur":28165, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":11, "ts":1754339328631847, "dur":180746, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328486659, "dur":8158, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328494817, "dur":602, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\netstandard.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328495420, "dur":637, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\msquic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328496057, "dur":532, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorrc.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328496589, "dur":640, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscorlib.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328497229, "dur":593, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordbi.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328497823, "dur":639, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore_amd64_amd64_7.0.1323.51816.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328498462, "dur":581, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\mscordaccore.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328499043, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\monolinker.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328499678, "dur":644, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328500322, "dur":839, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328501162, "dur":509, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328501672, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Mono.Cecil.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328502258, "dur":569, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Registry.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328502827, "dur":559, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Win32.Primitives.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328503386, "dur":632, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328504018, "dur":573, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.VisualBasic.Core.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328504591, "dur":645, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.DiaSymReader.Native.amd64.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328505236, "dur":558, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.CSharp.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328505794, "dur":576, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\Microsoft.Bcl.HashCode.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328506370, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\il2cpp\\build\\deploy\\il2cpp.exe" }}
,{ "pid":12345, "tid":12, "ts":1754339328494817, "dur":12132, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328513463, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Unity\\Editors\\6000.1.1f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\I18N.West.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328506949, "dur":8005, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328514955, "dur":3834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328518789, "dur":737, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328519543, "dur":740, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328520289, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328520800, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328520927, "dur":562, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328521496, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328522020, "dur":563, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328522588, "dur":520, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328523116, "dur":559, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328523680, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328524264, "dur":579, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328524849, "dur":509, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328525367, "dur":654, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328526029, "dur":508, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328526542, "dur":536, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328527086, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":12, "ts":1754339328527240, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328527363, "dur":354, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":12, "ts":1754339328527718, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328527831, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Unity/Builds/NLAME/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":12, "ts":1754339328527989, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328538923, "dur":41852, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/sharedassets0.resource" }}
,{ "pid":12345, "tid":12, "ts":1754339328580925, "dur":506, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328581432, "dur":99, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754339328582672, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328582883, "dur":268, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328583159, "dur":337, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328583501, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/WinPlayerBuildProgram/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1754339328583557, "dur":443, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328584908, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328585041, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328585199, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328585352, "dur":409, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328585802, "dur":533, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328587497, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328588767, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328588939, "dur":466, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328590594, "dur":524, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328591697, "dur":440, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328592143, "dur":1082, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328593263, "dur":703, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328595143, "dur":303, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328595453, "dur":560, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328596064, "dur":639, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328597555, "dur":437, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328599222, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328599426, "dur":271, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328599704, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328599943, "dur":209, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328600189, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328601607, "dur":479, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1754339328603218, "dur":9105, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Unity/Builds/NLAME/BLAME_Data/level0.resS" }}
,{ "pid":12345, "tid":12, "ts":1754339328612638, "dur":199944, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1754339328820883, "dur":1359, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 93864, "tid": 49661901, "ts": 1754339328824739, "dur": 6485, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 93864, "tid": 49661901, "ts": 1754339328831365, "dur": 1267, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 93864, "tid": 49661901, "ts": 1754339328823213, "dur": 9461, "ph": "X", "name": "Write chrome-trace events", "args": {} },
