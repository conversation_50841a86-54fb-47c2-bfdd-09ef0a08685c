using UnityEngine;
using System.Collections;
using System.Collections.Generic;

[ExecuteInEditMode]
public class DynamicFogSystem : MonoBehaviour
{
    [Header("Fog Visual Settings")]
    [SerializeField] private Color fogColor = new Color(0.9f, 0.9f, 0.9f, 0.1f);
    [Tooltip("Overall opacity of the fog")]
    [Range(0.01f, 1f)]
    [SerializeField] private float fogDensity = 0.3f;
    [Tooltip("Range of fog particle sizes")]
    [SerializeField] private Vector2 particleSizeRange = new Vector2(15f, 30f);
    [Tooltip("How much the fog particles should blend with each other")]
    [Range(0.1f, 10f)]
    [SerializeField] private float softness = 1f;
    
    [Header("Fog Movement")]
    [Tooltip("How fast the fog falls downward")]
    [SerializeField] private float fallSpeed = 0.8f;
    [Tooltip("Random movement to make fog appear less uniform")]
    [SerializeField] private float turbulence = 0.2f;
    [Tooltip("How fast the fog drifts horizontally")]
    [SerializeField] private float horizontalDriftSpeed = 0.1f;
    [Tooltip("How much the fog billows and changes size over time")]
    [SerializeField] private float billowStrength = 0.2f;
    
    [Header("Emission Settings")]
    [Tooltip("Number of fog emitters to create")]
    [Range(3, 20)]
    [SerializeField] private int fogEmitterCount = 6;
    [Tooltip("Maximum particles per emitter")]
    [SerializeField] private int maxParticlesPerEmitter = 150;
    [Tooltip("How many particles are emitted per second")]
    [SerializeField] private float emissionRate = 15f;
    [Tooltip("Minimum lifetime of each particle")]
    [SerializeField] private float minParticleLifetime = 15f;
    [Tooltip("Maximum lifetime of each particle")]
    [SerializeField] private float maxParticleLifetime = 25f;
    [Tooltip("Size of the emission area")]
    [SerializeField] private Vector3 emitterSizeRange = new Vector3(60f, 5f, 60f);
    [Tooltip("Height at which emitters spawn particles")]
    [SerializeField] private float emitterHeight = 30f;
    [Tooltip("Height at which fog emitters are recycled")]
    [SerializeField] private float recycleBelowHeight = -10f;
    [Tooltip("Height at which fog emitters respawn after recycling")]
    [SerializeField] private float respawnHeight = 40f;
    
    [Header("Player Tracking")]
    [Tooltip("Transform to follow (usually the player or camera)")]
    [SerializeField] private Transform targetToFollow;
    [Tooltip("How quickly the fog system follows the target")]
    [SerializeField] private float trackingSpeed = 2f;
    [Tooltip("Radius around the player where fog emitters can spawn")]
    [SerializeField] private float emitterSpawnRadius = 60f;
    
    // Internal fog system components
    private List<GameObject> fogEmitters = new List<GameObject>();
    private Texture2D sharedFogTexture;
    private Material sharedFogMaterial;
    
    private void OnEnable()
    {
        // Find player or camera if not assigned
        if (targetToFollow == null)
        {
            var camera = Camera.main;
            if (camera != null)
                targetToFollow = camera.transform;
        }
        
        // Create necessary assets
        CreateFogTexture();
        CreateFogMaterial();
        
        // Create fog system
        SetupFogSystem();
        
        // Start monitoring the emitters for recycling
        StartCoroutine(MonitorEmitters());
    }
    
    private void OnDisable()
    {
        // Clean up
        StopAllCoroutines();
        DestroyFogSystem();
    }
    
    private void CreateFogTexture()
    {
        // Create a soft circular gradient texture for fog particles
        int resolution = 256; // Higher resolution for smoother particles
        sharedFogTexture = new Texture2D(resolution, resolution, TextureFormat.RGBA32, false);
        sharedFogTexture.wrapMode = TextureWrapMode.Clamp;
        sharedFogTexture.filterMode = FilterMode.Trilinear; // Better filtering
        
        Color[] pixels = new Color[resolution * resolution];
        float center = resolution / 2f;
        
        for (int y = 0; y < resolution; y++)
        {
            for (int x = 0; x < resolution; x++)
            {
                // Calculate normalized distance from center (0 to 1)
                float distX = (x - center) / center;
                float distY = (y - center) / center;
                float distSqr = distX * distX + distY * distY;
                
                // Create soft circular gradient with smoother falloff
                float alpha = Mathf.Clamp01(1.0f - distSqr);
                
                // Apply a curve to make edges softer
                alpha = Mathf.Pow(alpha, softness);
                
                // Set color with calculated alpha
                pixels[y * resolution + x] = new Color(1f, 1f, 1f, alpha);
            }
        }
        
        sharedFogTexture.SetPixels(pixels);
        sharedFogTexture.Apply();
    }
    
    private void CreateFogMaterial()
    {
        // Create a material using the soft particle shader
        sharedFogMaterial = new Material(Shader.Find("Particles/Standard Unlit"));
        
        // Configure material properties
        sharedFogMaterial.SetTexture("_MainTex", sharedFogTexture);
        sharedFogMaterial.SetFloat("_Mode", 1); // Transparent blend mode
        sharedFogMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        sharedFogMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        sharedFogMaterial.SetInt("_ZWrite", 0);
        sharedFogMaterial.DisableKeyword("_ALPHATEST_ON");
        sharedFogMaterial.EnableKeyword("_ALPHABLEND_ON");
        sharedFogMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        sharedFogMaterial.renderQueue = 3000;
    }
    
    private void SetupFogSystem()
    {
        // Create a container for all the fog emitters
        for (int i = 0; i < fogEmitterCount; i++)
        {
            CreateFogEmitter(i);
        }
    }
    
    private void CreateFogEmitter(int index)
    {
        // Create emitter game object
        GameObject emitter = new GameObject($"FogEmitter_{index}");
        emitter.transform.SetParent(transform);
        
        // Position around the target (or at origin if no target)
        Vector3 emitterPosition;
        if (targetToFollow != null)
        {
            float angle = Random.Range(0f, 360f);
            float distance = Random.Range(0f, emitterSpawnRadius);
            
            // Random position around target
            emitterPosition = targetToFollow.position + new Vector3(
                Mathf.Cos(angle * Mathf.Deg2Rad) * distance,
                emitterHeight + Random.Range(-5f, 5f),
                Mathf.Sin(angle * Mathf.Deg2Rad) * distance
            );
        }
        else
        {
            // If no target, just use random position
            emitterPosition = new Vector3(
                Random.Range(-emitterSpawnRadius, emitterSpawnRadius),
                emitterHeight + Random.Range(-5f, 5f),
                Random.Range(-emitterSpawnRadius, emitterSpawnRadius)
            );
        }
        
        emitter.transform.position = emitterPosition;
        
        // Create particle system
        ParticleSystem particleSystem = emitter.AddComponent<ParticleSystem>();
        ParticleSystemRenderer renderer = emitter.GetComponent<ParticleSystemRenderer>();
        
        // Create instance of shared material with slight variation
        Material instanceMaterial = new Material(sharedFogMaterial);
        
        // Slightly vary the fog color for visual interest
        Color adjustedColor = fogColor;
        adjustedColor.r += Random.Range(-0.05f, 0.05f);
        adjustedColor.g += Random.Range(-0.05f, 0.05f);
        adjustedColor.b += Random.Range(-0.05f, 0.05f);
        adjustedColor.a = fogColor.a;
        
        instanceMaterial.SetColor("_TintColor", adjustedColor);
        
        // Main particle system settings
        var main = particleSystem.main;
        main.duration = 5.0f;
        main.loop = true;
        main.startLifetime = new ParticleSystem.MinMaxCurve(
            minParticleLifetime,
            maxParticleLifetime
        );
        main.startSpeed = 0;
        main.startSize = new ParticleSystem.MinMaxCurve(
            particleSizeRange.x * Random.Range(0.8f, 1.0f),
            particleSizeRange.y * Random.Range(1.0f, 1.2f)
        );
        main.startColor = adjustedColor;
        main.maxParticles = maxParticlesPerEmitter;
        main.simulationSpace = ParticleSystemSimulationSpace.World;
        main.gravityModifier = 0;
        
        // Emission settings
        var emission = particleSystem.emission;
        emission.enabled = true;
        emission.rateOverTime = emissionRate * Random.Range(0.8f, 1.2f);
        
        // Shape of emission area
        var shape = particleSystem.shape;
        shape.enabled = true;
        shape.shapeType = ParticleSystemShapeType.Box;
        shape.position = Vector3.zero;
        Vector3 emitterSize = new Vector3(
            emitterSizeRange.x * Random.Range(0.8f, 1.2f),
            emitterSizeRange.y * Random.Range(0.8f, 1.2f),
            emitterSizeRange.z * Random.Range(0.8f, 1.2f)
        );
        shape.scale = emitterSize;
        
        // Forces that move particles
        var force = particleSystem.forceOverLifetime;
        force.enabled = true;
        force.space = ParticleSystemSimulationSpace.World;
        
        // Variable forces for organic movement
        float emitterFallSpeed = fallSpeed * Random.Range(0.8f, 1.2f);
        force.y = new ParticleSystem.MinMaxCurve(-emitterFallSpeed);
        
        // Horizontal drift
        float driftX = horizontalDriftSpeed * Random.Range(-1f, 1f);
        float driftZ = horizontalDriftSpeed * Random.Range(-1f, 1f);
        force.x = new ParticleSystem.MinMaxCurve(driftX);
        force.z = new ParticleSystem.MinMaxCurve(driftZ);
        
        // Slight rotation
        var rotation = particleSystem.rotationOverLifetime;
        rotation.enabled = true;
        rotation.z = new ParticleSystem.MinMaxCurve(
            Random.Range(-0.3f, -0.1f),
            Random.Range(0.1f, 0.3f)
        );
        
        // Noise for more organic movement
        var noise = particleSystem.noise;
        noise.enabled = true;
        noise.strength = turbulence * Random.Range(0.8f, 1.2f);
        noise.frequency = Random.Range(0.05f, 0.2f);
        noise.octaveCount = 2; // More detail
        noise.scrollSpeed = Random.Range(0.1f, 0.3f);
        noise.quality = ParticleSystemNoiseQuality.Medium; // Better quality
        
        // Size changes over lifetime for billowing effect
        var sizeOverLifetime = particleSystem.sizeOverLifetime;
        sizeOverLifetime.enabled = true;
        
        AnimationCurve sizeCurve = new AnimationCurve();
        sizeCurve.AddKey(0f, 0.8f);    // Start at 80%
        sizeCurve.AddKey(0.2f, 1.0f);  // Grow to 100%
        
        // Add some random billow points
        float billowTime = Random.Range(0.4f, 0.6f);
        float billowSize = 1f + billowStrength * Random.Range(0.8f, 1.2f);
        sizeCurve.AddKey(billowTime, billowSize);
        
        sizeCurve.AddKey(0.8f, billowSize * 0.9f); // Slight reduction 
        sizeCurve.AddKey(1f, 0.7f);  // Shrink at end
        
        sizeOverLifetime.size = new ParticleSystem.MinMaxCurve(1f, sizeCurve);
        
        // Color/alpha changes over lifetime for fading
        var colorOverLifetime = particleSystem.colorOverLifetime;
        colorOverLifetime.enabled = true;
        
        Gradient fadeGradient = new Gradient();
        fadeGradient.SetKeys(
            new GradientColorKey[] { 
                new GradientColorKey(adjustedColor, 0f),
                new GradientColorKey(adjustedColor, 1f)
            },
            new GradientAlphaKey[] {
                new GradientAlphaKey(0f, 0f),
                new GradientAlphaKey(adjustedColor.a * fogDensity, 0.2f),
                new GradientAlphaKey(adjustedColor.a * fogDensity, 0.8f),
                new GradientAlphaKey(0f, 1f)
            }
        );
        colorOverLifetime.color = fadeGradient;
        
        // Renderer settings
        renderer.material = instanceMaterial;
        renderer.renderMode = ParticleSystemRenderMode.Billboard;
        renderer.sortMode = ParticleSystemSortMode.Distance;
        renderer.minParticleSize = 0.01f;
        renderer.maxParticleSize = 0.3f;
        
        // Start the system
        particleSystem.Play();
        fogEmitters.Add(emitter);
    }
    
    private void Update()
    {
        // Follow target if one is set
        if (targetToFollow != null)
        {
            Vector3 targetPos = new Vector3(
                targetToFollow.position.x, 
                transform.position.y,
                targetToFollow.position.z
            );
            
            transform.position = Vector3.Lerp(
                transform.position, 
                targetPos, 
                Time.deltaTime * trackingSpeed
            );
        }
    }
    
    private IEnumerator MonitorEmitters()
    {
        WaitForSeconds wait = new WaitForSeconds(2f);
        
        while (true)
        {
            if (targetToFollow != null)
            {
                // Check each emitter and recycle if needed
                foreach (GameObject emitter in fogEmitters)
                {
                    if (emitter != null && emitter.transform.position.y < recycleBelowHeight)
                    {
                        RecycleEmitter(emitter);
                    }
                }
            }
            
            yield return wait;
        }
    }
    
    private void RecycleEmitter(GameObject emitter)
    {
        // Calculate new position for recycled emitter
        float angle = Random.Range(0f, 360f);
        float distance = Random.Range(0f, emitterSpawnRadius);
            
        Vector3 newPosition = targetToFollow.position + new Vector3(
            Mathf.Cos(angle * Mathf.Deg2Rad) * distance,
            targetToFollow.position.y + respawnHeight + Random.Range(-5f, 5f),
            Mathf.Sin(angle * Mathf.Deg2Rad) * distance
        );
        
        emitter.transform.position = newPosition;
        
        // Optionally reset the particle system
        ParticleSystem ps = emitter.GetComponent<ParticleSystem>();
        if (ps != null)
        {
            ps.Clear();
            ps.Play();
        }
    }
    
    private void DestroyFogSystem()
    {
        // Clean up game objects
        foreach (GameObject emitter in fogEmitters)
        {
            if (emitter != null)
            {
                if (Application.isPlaying)
                    Destroy(emitter);
                else
                    DestroyImmediate(emitter);
            }
        }
        
        fogEmitters.Clear();
    }
    
    // For editor functionality - recreate the fog system when values change
    private void OnValidate()
    {
        if (Application.isPlaying)
        {
            DestroyFogSystem();
            
            // Recreate assets with new settings
            CreateFogTexture();
            CreateFogMaterial();
            SetupFogSystem();
            
            // Restart monitoring
            StopAllCoroutines();
            StartCoroutine(MonitorEmitters());
        }
    }
}