Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-04T00:29:10Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
59354
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [217904]  Target information:

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904] Host joined multi-casting on [***********:54997]...
Player connection [217904] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2602.02 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56780
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.020709 seconds.
- Loaded All Assemblies, in  2.092 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.275 seconds
Domain Reload Profiling: 4319ms
	BeginReloadAssembly (614ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (252ms)
	RebuildNativeTypeToScriptingClass (45ms)
	initialDomainReloadingComplete (271ms)
	LoadAllAssembliesAndSetupDomain (860ms)
		LoadAssemblies (545ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (822ms)
			TypeCache.Refresh (819ms)
				TypeCache.ScanAssembly (748ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2277ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1985ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (225ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (720ms)
			ProcessInitializeOnLoadAttributes (629ms)
			ProcessInitializeOnLoadMethodAttributes (362ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.10 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 13.167 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 50.85 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.408 seconds
Domain Reload Profiling: 15501ms
	BeginReloadAssembly (8038ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (101ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (345ms)
	RebuildNativeTypeToScriptingClass (41ms)
	initialDomainReloadingComplete (297ms)
	LoadAllAssembliesAndSetupDomain (4371ms)
		LoadAssemblies (3404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1375ms)
			TypeCache.Refresh (842ms)
				TypeCache.ScanAssembly (814ms)
			BuildScriptInfoCaches (445ms)
			ResolveRequiredComponents (76ms)
	FinalizeReload (2409ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1953ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (440ms)
			ProcessInitializeOnLoadAttributes (734ms)
			ProcessInitializeOnLoadMethodAttributes (736ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 30.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 553 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7912 unused Assets / (3.7 MB). Loaded Objects now: 8979.
Memory consumption went from 384.0 MB to 380.2 MB.
Total: 22.337200 ms (FindLiveObjects: 1.394300 ms CreateObjectMapping: 2.677300 ms MarkObjects: 13.427600 ms  DeleteObjects: 4.836200 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.299 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 68.98 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.467 seconds
Domain Reload Profiling: 6707ms
	BeginReloadAssembly (1247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (162ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (129ms)
	RebuildCommonClasses (130ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (2716ms)
		LoadAssemblies (2646ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (841ms)
			TypeCache.Refresh (54ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (729ms)
			ResolveRequiredComponents (43ms)
	FinalizeReload (2468ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (743ms)
			ProcessInitializeOnLoadAttributes (877ms)
			ProcessInitializeOnLoadMethodAttributes (213ms)
			AfterProcessingInitializeOnLoad (45ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (60ms)
Refreshing native plugins compatible for Editor in 622.93 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9002.
Memory consumption went from 320.4 MB to 317.5 MB.
Total: 1742.553500 ms (FindLiveObjects: 40.312100 ms CreateObjectMapping: 536.188300 ms MarkObjects: 896.034000 ms  DeleteObjects: 270.017400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 547161.172789 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml
  artifactKey: Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml using Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'cc424780afe3b3885452e0cf81094bce') in 0.1381062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml
  artifactKey: Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml using Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'cf7db956ae02fa80280494506f8e9b74') in 0.0367573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.652 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.43 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.460 seconds
Domain Reload Profiling: 12004ms
	BeginReloadAssembly (1527ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (230ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (286ms)
	RebuildCommonClasses (167ms)
	RebuildNativeTypeToScriptingClass (44ms)
	initialDomainReloadingComplete (177ms)
	LoadAllAssembliesAndSetupDomain (2629ms)
		LoadAssemblies (2654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (597ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (525ms)
			ResolveRequiredComponents (36ms)
	FinalizeReload (7461ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (74ms)
			BeforeProcessingInitializeOnLoad (1697ms)
			ProcessInitializeOnLoadAttributes (4137ms)
			ProcessInitializeOnLoadMethodAttributes (507ms)
			AfterProcessingInitializeOnLoad (69ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (162ms)
Refreshing native plugins compatible for Editor in 2552.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9007.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 186.288700 ms (FindLiveObjects: 1.828800 ms CreateObjectMapping: 7.707300 ms MarkObjects: 143.321100 ms  DeleteObjects: 33.430000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.929 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.46 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.236 seconds
Domain Reload Profiling: 6142ms
	BeginReloadAssembly (820ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (83ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (274ms)
	RebuildCommonClasses (162ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (3838ms)
		LoadAssemblies (3145ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (876ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (823ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1237ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (914ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 26.91 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.2 MB). Loaded Objects now: 9009.
Memory consumption went from 317.7 MB to 312.5 MB.
Total: 17.457900 ms (FindLiveObjects: 1.533300 ms CreateObjectMapping: 1.200500 ms MarkObjects: 10.340300 ms  DeleteObjects: 4.380700 ms)

Prepare: number of updated asset objects reloaded= 0
