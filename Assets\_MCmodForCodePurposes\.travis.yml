language: java
jdk:
  - openjdk16

before_cache:
  - rm -f  $HOME/.gradle/caches/modules-2/modules-2.lock
  - rm -fr $HOME/.gradle/caches/*/plugin-resolution/
cache:
  directories:
    - $HOME/.gradle/caches/
    - $HOME/.gradle/wrapper/

jobs:
  include:
    - stage: check
      script: ./gradlew check
    - stage: publish
      script: ./gradlew build publish
      on:
        branch: master

stages:
  - check
  - name: publish
    if: env(ACCESS_KEY)
