buildscript {
    dependencies {
        classpath 'com.github.dexman545:Outlet:1.6.1'
    }
}
plugins {
    id 'java-library'
    id 'fabric-loom' version '1.10-SNAPSHOT'
    id 'maven-publish'
    id 'com.modrinth.minotaur' version '2.+'
    id 'org.ajoberstar.reckon' version '0.13.1'
}
apply plugin: 'io.github.dexman545.outlet'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
    withSourcesJar()
}

outlet.allowSnapshotsForProject = false
outlet.mcVersionRange = project.minecraft_version_range

group = project.group
description = project.displayname
archivesBaseName = project.name

loom {
    mixin.defaultRefmapName = 'presencefootsteps.mixin.refmap.json'
}

reckon {
    scopeFromProp()
    stageFromProp('beta', 'rc', 'final')
}

repositories {
    flatDir { dirs 'lib' }
    mavenLocal()
    maven { name 'pehkui'; url 'https://jitpack.io' }
    maven { name 'modmenu'; url 'https://maven.terraformersmc.com/releases' }
    maven { name 'minelp'; url 'https://repo.minelittlepony-mod.com/maven/snapshot' }
    maven { name 'minelp-release'; url 'https://repo.minelittlepony-mod.com/maven/release' }
}

dependencies {
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modApi "net.fabricmc:fabric-loader:${project.loader_version}"

    modApi fabricApi.module("fabric-api-base", project.fabric_version)
    modApi fabricApi.module("fabric-lifecycle-events-v1", project.fabric_version)
    modApi fabricApi.module("fabric-key-binding-api-v1", project.fabric_version)
    modApi fabricApi.module("fabric-screen-api-v1", project.fabric_version)
    modApi fabricApi.module("fabric-resource-loader-v0", project.fabric_version)
    modApi fabricApi.module("fabric-block-api-v1", project.fabric_version)

    modApi "com.minelittlepony:kirin:${project.kirin_version}"
    include "com.minelittlepony:kirin:${project.kirin_version}"

    if (project.use_minelp == '1') {
      modCompileOnly "com.minelittlepony:minelittlepony:${project.minelp_version}"
    } else {
      modCompileOnly "com.minelittlepony:minelittlepony-dummy:${project.minelp_version}"
    }
    modCompileOnly("com.terraformersmc:modmenu:${project.modmenu_version}")

    // modImplementation "com.github.Virtuoel:Pehkui:${project.pehkui_version}"
}

processResources {
    inputs.property "version", project.version.toString()

    filesMatching("fabric.mod.json") {
        expand "version": project.version.toString(),
                "minecraftVersion": project.minecraft_version_range,
                "loaderVersion": ">=${project.loader_version}",
                "fabricVersion": ">=${project.fabric_version}",
                "kirinVersion": ">=${kirin_version}",
                "minelpVersion": ">=${project.minelp_version}"
    }

    from 'LICENSE'
}

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
}

tasks.modrinth {
    onlyIf {
      def stage = version.version.stage.value
      stage == null || stage.name != 'beta'
    }
}

modrinth {
    token = System.env.MODRINTH_KEY
    projectId = project.modrinth_project_id
    //featured = true
    versionNumber = version.toString()
    versionName = archivesBaseName + '-' + version
    changelog = "[Changelog](https://github.com/Sollace/Presence-Footsteps/releases/tag/${version.toString()})"
    loaders = ['fabric', 'quilt']
    uploadFile = remapJar
    outlet.mcVersions().each{ver ->
      gameVersions.add ver
    }
    dependencies {
       required.project 'P7dR8mSH' // Fabric API
       optional.project 'JBjInUXM' // Mine Little Pony
       embedded.project '9aNz8Zqn' // Kirin
    }
}
